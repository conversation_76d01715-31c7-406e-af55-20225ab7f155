#!/usr/bin/env python3
"""
Test script for Document Rectification System.

Provides comprehensive testing including unit tests for individual modules
and integration tests for the complete pipeline.
"""

import os
import sys
import unittest
import numpy as np
import cv2
import tempfile
import shutil
from pathlib import Path

# Add the document_rectification module to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from document_rectification import DocumentRectifier, Config
from document_rectification.types import TargetSize, NotEnoughCornersError
from document_rectification.preprocess import preprocess_image, normalize_illumination
from document_rectification.markers import find_four_markers, order_corners
from document_rectification.geometry import warp_perspective, deskew_hough
from document_rectification.binarize import binarize_document


class TestPreprocessing(unittest.TestCase):
    """Test preprocessing functions."""
    
    def setUp(self):
        """Create test image."""
        self.test_image = np.random.randint(0, 255, (500, 400, 3), dtype=np.uint8)
        self.config = Config()
    
    def test_preprocess_image(self):
        """Test complete preprocessing pipeline."""
        enhanced, original, stats = preprocess_image(self.test_image, self.config)
        
        # Check outputs
        self.assertEqual(len(enhanced.shape), 2)  # Should be grayscale
        self.assertEqual(enhanced.shape, (500, 400))
        self.assertEqual(original.shape, self.test_image.shape)
        self.assertIsInstance(stats, dict)
        self.assertIn('original_mean', stats)
    
    def test_normalize_illumination(self):
        """Test illumination normalization."""
        gray = cv2.cvtColor(self.test_image, cv2.COLOR_BGR2GRAY)
        normalized = normalize_illumination(gray, self.config.illumination)
        
        self.assertEqual(normalized.shape, gray.shape)
        self.assertEqual(normalized.dtype, np.uint8)


class TestMarkerDetection(unittest.TestCase):
    """Test marker detection functions."""
    
    def setUp(self):
        """Create test image with synthetic markers."""
        self.image = np.ones((600, 800), dtype=np.uint8) * 200  # Gray background
        
        # Add 4 black square markers at corners
        marker_size = 20
        positions = [(50, 50), (750, 50), (750, 550), (50, 550)]
        
        for x, y in positions:
            self.image[y:y+marker_size, x:x+marker_size] = 0
        
        self.config = Config()
    
    def test_find_four_markers(self):
        """Test marker detection."""
        corners = find_four_markers(self.image, self.config.marker)
        
        self.assertIsNotNone(corners)
        self.assertEqual(len(corners), 4)
        
        # Check if corners are approximately at expected positions
        expected = np.array([[60, 60], [760, 60], [760, 560], [60, 560]], dtype=np.float32)
        
        # Allow some tolerance
        for i, corner in enumerate(corners):
            distance = np.linalg.norm(corner - expected[i])
            self.assertLess(distance, 20, f"Corner {i} too far from expected position")
    
    def test_order_corners(self):
        """Test corner ordering."""
        # Random order corners
        corners = np.array([[100, 100], [200, 200], [200, 100], [100, 200]], dtype=np.float32)
        ordered = order_corners(corners)
        
        # Should be ordered as TL, TR, BR, BL
        expected_order = np.array([[100, 100], [200, 100], [200, 200], [100, 200]], dtype=np.float32)
        
        np.testing.assert_array_almost_equal(ordered, expected_order)


class TestGeometry(unittest.TestCase):
    """Test geometry transformation functions."""
    
    def setUp(self):
        """Create test data."""
        self.image = np.random.randint(0, 255, (400, 300, 3), dtype=np.uint8)
        self.corners = np.array([[50, 50], [250, 50], [250, 350], [50, 350]], dtype=np.float32)
        self.target_size = TargetSize(width=400, height=600)
    
    def test_warp_perspective(self):
        """Test perspective warping."""
        warped, H = warp_perspective(self.image, self.corners, self.target_size)
        
        self.assertEqual(warped.shape, (600, 400, 3))
        self.assertEqual(H.shape, (3, 3))
        self.assertIsNotNone(H)
    
    def test_deskew_hough(self):
        """Test deskewing with Hough transform."""
        config = Config()
        deskewed, angle = deskew_hough(self.image, config.hough)
        
        self.assertEqual(deskewed.shape, self.image.shape)
        self.assertIsInstance(angle, float)


class TestBinarization(unittest.TestCase):
    """Test binarization functions."""
    
    def setUp(self):
        """Create test image."""
        self.image = np.random.randint(0, 255, (300, 400, 3), dtype=np.uint8)
        self.config = Config()
    
    def test_binarize_document(self):
        """Test document binarization."""
        binary, stats = binarize_document(self.image, self.config)
        
        self.assertEqual(len(binary.shape), 2)  # Should be grayscale
        self.assertEqual(binary.shape, (300, 400))
        self.assertTrue(np.all((binary == 0) | (binary == 255)))  # Should be binary
        self.assertIsInstance(stats, dict)


class TestIntegration(unittest.TestCase):
    """Integration tests for complete pipeline."""
    
    def setUp(self):
        """Create test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.config = Config()
        self.rectifier = DocumentRectifier(self.config)
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir)
    
    def create_synthetic_omr_image(self, width=800, height=1000):
        """Create a synthetic OMR-like image for testing."""
        # Create white background
        image = np.ones((height, width, 3), dtype=np.uint8) * 255
        
        # Add 4 corner markers (black squares)
        marker_size = 30
        positions = [(50, 50), (width-80, 50), (width-80, height-80), (50, height-80)]
        
        for x, y in positions:
            image[y:y+marker_size, x:x+marker_size] = 0
        
        # Add some text-like content
        for i in range(10, height-50, 40):
            cv2.rectangle(image, (100, i), (width-100, i+20), (0, 0, 0), 2)
        
        # Add some circles (OMR bubbles)
        for row in range(5):
            for col in range(10):
                center = (150 + col * 60, 200 + row * 80)
                cv2.circle(image, center, 15, (0, 0, 0), 2)
        
        return image
    
    def test_complete_pipeline_synthetic(self):
        """Test complete pipeline with synthetic image."""
        # Create synthetic OMR image
        image = self.create_synthetic_omr_image()
        
        # Save to temporary file
        image_path = os.path.join(self.temp_dir, "test_omr.jpg")
        cv2.imwrite(image_path, image)
        
        # Process the image
        result = self.rectifier.process_document(
            image_path=image_path,
            output_dir=self.temp_dir,
            save_intermediate=True
        )
        
        # Check results
        self.assertTrue(result.success, f"Processing failed: {result.error_message}")
        self.assertIsNotNone(result.warped_path)
        self.assertIsNotNone(result.binarized_path)
        self.assertTrue(os.path.exists(result.warped_path))
        self.assertTrue(os.path.exists(result.binarized_path))
        
        # Check quality metrics
        self.assertEqual(result.quality.marker_count, 4)
        self.assertLess(result.quality.skew_residual_deg, 1.0)  # Should be well-aligned
        self.assertGreater(result.quality.edge_strength, 0.1)  # Should have decent edges
    
    def test_batch_processing(self):
        """Test batch processing functionality."""
        # Create multiple synthetic images
        for i in range(3):
            image = self.create_synthetic_omr_image()
            image_path = os.path.join(self.temp_dir, f"test_omr_{i}.jpg")
            cv2.imwrite(image_path, image)
        
        # Process batch
        output_dir = os.path.join(self.temp_dir, "batch_output")
        results = self.rectifier.batch_process(self.temp_dir, output_dir)
        
        # Check results
        self.assertEqual(len(results), 3)
        
        for filename, result in results.items():
            self.assertTrue(result.success, f"Failed to process {filename}: {result.error_message}")
        
        # Check summary
        summary = self.rectifier.get_processing_summary(results)
        self.assertEqual(summary['total_files'], 3)
        self.assertEqual(summary['successful'], 3)
        self.assertEqual(summary['failed'], 0)
        self.assertEqual(summary['success_rate'], 1.0)
    
    def test_error_handling(self):
        """Test error handling with invalid inputs."""
        # Test with non-existent file
        result = self.rectifier.process_document("nonexistent.jpg", self.temp_dir)
        self.assertFalse(result.success)
        self.assertIn("Could not load image", result.error_message)
        
        # Test with image without markers
        blank_image = np.ones((400, 300, 3), dtype=np.uint8) * 128
        image_path = os.path.join(self.temp_dir, "blank.jpg")
        cv2.imwrite(image_path, blank_image)
        
        result = self.rectifier.process_document(image_path, self.temp_dir)
        self.assertFalse(result.success)
        # Should fail due to insufficient corners


class TestConfigurationValidation(unittest.TestCase):
    """Test configuration validation and edge cases."""
    
    def test_config_defaults(self):
        """Test default configuration values."""
        config = Config()
        
        # Check target size
        self.assertEqual(config.target_size.width, 2480)
        self.assertEqual(config.target_size.height, 3508)
        
        # Check CLAHE settings
        self.assertEqual(config.clahe.clip, 2.2)
        self.assertEqual(config.clahe.tile, (8, 8))
        
        # Check marker settings
        self.assertEqual(config.marker.min_area_ratio, 0.0002)
        self.assertEqual(config.marker.square_ratio_min, 0.75)
    
    def test_custom_config(self):
        """Test custom configuration creation."""
        config = Config()
        
        # Modify settings
        config.target_size.width = 1000
        config.clahe.clip = 3.0
        config.marker.min_area_ratio = 0.001
        
        # Verify changes
        self.assertEqual(config.target_size.width, 1000)
        self.assertEqual(config.clahe.clip, 3.0)
        self.assertEqual(config.marker.min_area_ratio, 0.001)


def run_performance_test():
    """Run performance test with timing."""
    import time
    
    print("\n=== Performance Test ===")
    
    # Create test image
    image = np.random.randint(0, 255, (1000, 800, 3), dtype=np.uint8)
    
    # Add markers
    marker_size = 40
    positions = [(50, 50), (750, 50), (750, 950), (50, 950)]
    for x, y in positions:
        image[y:y+marker_size, x:x+marker_size] = 0
    
    # Create rectifier
    rectifier = DocumentRectifier()
    
    # Time the processing
    start_time = time.time()
    
    with tempfile.TemporaryDirectory() as temp_dir:
        image_path = os.path.join(temp_dir, "perf_test.jpg")
        cv2.imwrite(image_path, image)
        
        result = rectifier.process_document(image_path, temp_dir)
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"Processing time: {processing_time:.2f} seconds")
    print(f"Success: {result.success}")
    
    if result.success:
        print(f"Quality metrics:")
        print(f"  - Markers: {result.quality.marker_count}")
        print(f"  - Skew residual: {result.quality.skew_residual_deg:.3f}°")
        print(f"  - Edge strength: {result.quality.edge_strength:.3f}")


def main():
    """Main test runner."""
    print("Document Rectification System - Test Suite")
    print("=" * 50)
    
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run performance test
    run_performance_test()


if __name__ == "__main__":
    main()
