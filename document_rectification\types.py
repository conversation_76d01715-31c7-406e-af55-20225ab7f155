"""
Data types and configuration classes for document rectification system.
"""

from dataclasses import dataclass, field
from typing import List, Tuple, Optional, Dict, Any
import numpy as np


@dataclass
class TargetSize:
    """Target output dimensions."""
    width: int = 2480   # A4 @ 300dpi
    height: int = 3508


@dataclass
class IlluminationConfig:
    """Illumination normalization settings."""
    morph_k: int = 41  # Morphological kernel size (25-61)


@dataclass
class CLAHEConfig:
    """CLAHE (Contrast Limited Adaptive Histogram Equalization) settings."""
    clip: float = 2.2      # Clip limit (1.5-3.0)
    tile: Tuple[int, int] = (8, 8)  # Tile grid size


@dataclass
class DenoiseConfig:
    """Denoising filter settings."""
    method: str = "bilateral"  # "bilateral" or "gaussian"
    d: int = 7                 # Diameter for bilateral
    sigmaC: float = 50.0       # Color sigma for bilateral
    sigmaS: float = 50.0       # Space sigma for bilateral


@dataclass
class UnsharpConfig:
    """Unsharp masking settings."""
    sigma: float = 1.0    # Gaussian blur sigma
    amount: float = 1.0   # Sharpening amount


@dataclass
class MarkerConfig:
    """Marker detection settings."""
    min_area_ratio: float = 0.0002    # Minimum area relative to image
    square_ratio_min: float = 0.75    # Minimum aspect ratio for square
    approx_epsilon: float = 0.04      # Contour approximation epsilon


@dataclass
class HoughConfig:
    """Hough line detection settings."""
    canny_low: int = 50       # Canny low threshold
    canny_high: int = 150     # Canny high threshold
    thresh: int = 250         # Hough threshold
    max_lines: int = 200      # Maximum lines to consider


@dataclass
class AdaptiveConfig:
    """Adaptive thresholding settings."""
    method: str = "gaussian"  # "gaussian" or "mean"
    block: int = 35          # Block size
    C: float = 10.0          # Constant subtracted from mean


@dataclass
class BinarizeConfig:
    """Binarization post-processing settings."""
    median_blur: int = 3     # Median blur kernel size


@dataclass
class FallbackConfig:
    """Fallback processing options."""
    page_contour: bool = True      # Enable page contour detection
    tps_enable: bool = False       # Enable thin-plate spline correction
    tps_grid: Tuple[int, int] = (20, 20)  # TPS grid size


@dataclass
class Config:
    """Complete configuration for document rectification."""
    target_size: TargetSize = field(default_factory=TargetSize)
    illumination: IlluminationConfig = field(default_factory=IlluminationConfig)
    clahe: CLAHEConfig = field(default_factory=CLAHEConfig)
    denoise: DenoiseConfig = field(default_factory=DenoiseConfig)
    unsharp: UnsharpConfig = field(default_factory=UnsharpConfig)
    marker: MarkerConfig = field(default_factory=MarkerConfig)
    hough: HoughConfig = field(default_factory=HoughConfig)
    adaptive: AdaptiveConfig = field(default_factory=AdaptiveConfig)
    binarize: BinarizeConfig = field(default_factory=BinarizeConfig)
    fallback: FallbackConfig = field(default_factory=FallbackConfig)


@dataclass
class QualityMetrics:
    """Quality assessment metrics."""
    marker_count: int = 0              # Number of markers detected
    skew_residual_deg: float = 0.0     # Remaining skew after correction
    edge_strength: float = 0.0         # Edge strength measure (0-1)


@dataclass
class DebugInfo:
    """Debug information for troubleshooting."""
    corners_src: List[Tuple[float, float]] = field(default_factory=list)  # Source corners
    corners_dst: List[Tuple[float, float]] = field(default_factory=list)  # Destination corners
    detection_method: str = ""          # "markers" or "page_contour"
    preprocessing_stats: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Result:
    """Complete result from document rectification."""
    warped_path: Optional[str] = None           # Path to warped image
    binarized_path: Optional[str] = None        # Path to binarized image
    angle_deg: float = 0.0                      # Rotation angle applied
    H_3x3: Optional[np.ndarray] = None          # Homography matrix
    quality: QualityMetrics = field(default_factory=QualityMetrics)
    debug: DebugInfo = field(default_factory=DebugInfo)
    success: bool = False                       # Overall success flag
    error_message: str = ""                     # Error description if failed


# Error codes
class RectificationError(Exception):
    """Base exception for rectification errors."""
    pass


class NotEnoughCornersError(RectificationError):
    """Raised when insufficient corners are detected."""
    pass


class ProcessingError(RectificationError):
    """Raised when processing fails."""
    pass
