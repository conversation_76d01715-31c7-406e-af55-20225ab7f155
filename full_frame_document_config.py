# Optimized Configuration for Full-Frame Documents
# Based on analysis of ptntest.jpg

from document_rectification import Config
from document_rectification.types import TargetSize

def create_full_frame_document_config():
    """Configuration for documents that fill the entire image frame."""
    config = Config()
    
    # Standard A4 output
    config.target_size = TargetSize(width=2480, height=3508)
    
    # Gentle preprocessing to preserve detail
    config.illumination.morph_k = 25        # Less aggressive background removal
    config.clahe.clip = 1.8                 # Preserve detail
    config.clahe.tile = (4, 4)              # Smaller tiles
    
    # Minimal denoising
    config.denoise.d = 5
    config.denoise.sigmaC = 30.0
    config.denoise.sigmaS = 30.0
    
    # Light sharpening
    config.unsharp.sigma = 0.8
    config.unsharp.amount = 0.8
    
    # Very sensitive marker detection (may still fail)
    config.marker.min_area_ratio = 0.000001
    config.marker.square_ratio_min = 0.3
    config.marker.approx_epsilon = 0.08
    
    # Sensitive line detection
    config.hough.canny_low = 30
    config.hough.canny_high = 100
    config.hough.thresh = 100
    config.hough.max_lines = 500
    
    # Fine adaptive thresholding
    config.adaptive.block = 25
    config.adaptive.C = 8.0
    
    # Minimal post-processing
    config.binarize.median_blur = 1
    
    # Enable fallbacks
    config.fallback.page_contour = True
    config.fallback.tps_enable = False
    
    return config

# Usage example:
# config = create_full_frame_document_config()
# rectifier = DocumentRectifier(config)
