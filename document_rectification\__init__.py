"""
Document Rectification System for OMR Papers
============================================

A comprehensive system for perspective correction, deskewing, and binarization
of scanned exam papers and OMR sheets with 4 corner markers.

Main components:
- Pipeline: Main orchestrator
- Preprocessing: Noise reduction, illumination normalization
- Marker detection: Find 4 corner markers or page contours
- Geometry: Perspective warping and deskewing
- Binarization: Adaptive thresholding for final processing
- Metrics: Quality assessment
- Alignment: Fine-tuning (optional)
"""

from .pipeline import DocumentRectifier
from .types import Config, Result, DebugInfo, QualityMetrics

__version__ = "1.0.0"
__all__ = ["DocumentRectifier", "Config", "Result", "DebugInfo", "QualityMetrics"]
