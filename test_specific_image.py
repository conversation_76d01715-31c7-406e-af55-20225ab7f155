#!/usr/bin/env python3
"""
Test script for specific image: ptntest.jpg
"""

import os
import sys
import cv2
import numpy as np
import logging

# Add the document_rectification module to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from document_rectification import DocumentRectifier, Config
from document_rectification.types import TargetSize, CLAHEConfig, MarkerConfig


def setup_logging():
    """Setup detailed logging to see processing steps."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('ptntest_processing.log')
        ]
    )


def create_omr_optimized_config():
    """Create configuration optimized for OMR papers."""
    config = Config()
    
    # Target A4 size at 300 DPI
    config.target_size = TargetSize(width=2480, height=3508)
    
    # Enhanced preprocessing for OMR papers
    config.illumination.morph_k = 41  # Good for uneven lighting
    config.clahe.clip = 2.5           # Slightly higher contrast
    config.clahe.tile = (8, 8)        # Standard tile size
    
    # Denoising for scanned documents
    config.denoise.method = "bilateral"
    config.denoise.d = 7
    config.denoise.sigmaC = 50.0
    config.denoise.sigmaS = 50.0
    
    # Unsharp masking for better edge definition
    config.unsharp.sigma = 1.0
    config.unsharp.amount = 1.2  # Slightly more sharpening
    
    # Marker detection - optimized for corner markers
    config.marker.min_area_ratio = 0.0002    # Standard marker size
    config.marker.square_ratio_min = 0.7     # Allow slightly rectangular
    config.marker.approx_epsilon = 0.04      # Standard approximation
    
    # Hough parameters for line detection
    config.hough.canny_low = 50
    config.hough.canny_high = 150
    config.hough.thresh = 200        # Slightly lower threshold
    config.hough.max_lines = 300     # More lines for better detection
    
    # Adaptive thresholding for final binarization
    config.adaptive.method = "gaussian"
    config.adaptive.block = 35
    config.adaptive.C = 10.0
    
    # Post-processing
    config.binarize.median_blur = 3
    
    # Enable fallback mechanisms
    config.fallback.page_contour = True
    config.fallback.tps_enable = False  # Start with disabled
    
    return config


def test_image_processing(image_path, output_dir="ptntest_output"):
    """Test processing of the specific image."""
    
    print("=" * 60)
    print("TESTING DOCUMENT RECTIFICATION SYSTEM")
    print("=" * 60)
    print(f"Input image: {image_path}")
    print(f"Output directory: {output_dir}")
    
    # Check if image exists
    if not os.path.exists(image_path):
        print(f"❌ Error: Image file not found: {image_path}")
        return False
    
    # Load and display image info
    image = cv2.imread(image_path)
    if image is None:
        print(f"❌ Error: Could not load image: {image_path}")
        return False
    
    h, w, c = image.shape
    print(f"📊 Image info: {w}x{h} pixels, {c} channels")
    print(f"📊 File size: {os.path.getsize(image_path) / 1024:.1f} KB")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Create optimized configuration
    config = create_omr_optimized_config()
    print(f"⚙️  Configuration: OMR-optimized")
    
    # Create rectifier
    rectifier = DocumentRectifier(config)
    
    print("\n🔄 Starting processing...")
    
    try:
        # Process the document
        result = rectifier.process_document(
            image_path=image_path,
            output_dir=output_dir,
            save_intermediate=True  # Save all processing steps
        )
        
        print("\n" + "=" * 60)
        print("PROCESSING RESULTS")
        print("=" * 60)
        
        if result.success:
            print("✅ Processing completed successfully!")
            
            # Display results
            print(f"\n📁 Output files:")
            print(f"   • Warped image: {result.warped_path}")
            print(f"   • Binary image: {result.binarized_path}")
            
            print(f"\n📐 Geometric corrections:")
            print(f"   • Skew angle applied: {result.angle_deg:.3f}°")
            
            print(f"\n📊 Quality metrics:")
            print(f"   • Markers detected: {result.quality.marker_count}/4")
            print(f"   • Skew residual: {result.quality.skew_residual_deg:.3f}°")
            print(f"   • Edge strength: {result.quality.edge_strength:.3f}")
            
            print(f"\n🔍 Detection details:")
            print(f"   • Method used: {result.debug.detection_method}")
            print(f"   • Source corners: {len(result.debug.corners_src)} points")
            
            # Display corner coordinates
            if result.debug.corners_src:
                print(f"   • Corner positions:")
                labels = ["Top-Left", "Top-Right", "Bottom-Right", "Bottom-Left"]
                for i, (corner, label) in enumerate(zip(result.debug.corners_src, labels)):
                    print(f"     {label}: ({corner[0]:.1f}, {corner[1]:.1f})")
            
            # Check quality criteria
            print(f"\n✅ Quality assessment:")
            if result.quality.marker_count == 4:
                print("   ✅ All 4 corners detected")
            else:
                print(f"   ⚠️  Only {result.quality.marker_count} corners detected")
            
            if result.quality.skew_residual_deg < 0.5:
                print("   ✅ Excellent skew correction (< 0.5°)")
            elif result.quality.skew_residual_deg < 1.0:
                print("   ✅ Good skew correction (< 1.0°)")
            else:
                print(f"   ⚠️  High skew residual: {result.quality.skew_residual_deg:.3f}°")
            
            if result.quality.edge_strength > 0.3:
                print("   ✅ Strong edge definition")
            elif result.quality.edge_strength > 0.1:
                print("   ✅ Adequate edge definition")
            else:
                print(f"   ⚠️  Weak edge definition: {result.quality.edge_strength:.3f}")
            
            # List intermediate files
            print(f"\n📁 Intermediate processing files:")
            intermediate_files = [
                "01_preprocessed.jpg",
                "02_warped.jpg", 
                "03_deskewed.jpg",
                "05_binary.jpg"
            ]
            
            for filename in intermediate_files:
                filepath = os.path.join(output_dir, filename)
                if os.path.exists(filepath):
                    print(f"   • {filename}")
            
            return True
            
        else:
            print("❌ Processing failed!")
            print(f"Error: {result.error_message}")
            
            # Provide troubleshooting suggestions
            print(f"\n🔧 Troubleshooting suggestions:")
            if "corners" in result.error_message.lower():
                print("   • Try adjusting marker detection sensitivity")
                print("   • Check if corner markers are clearly visible")
                print("   • Enable page contour fallback")
            
            return False
            
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False


def try_alternative_configs(image_path, output_dir="ptntest_output"):
    """Try different configurations if the first one fails."""
    
    print("\n" + "=" * 60)
    print("TRYING ALTERNATIVE CONFIGURATIONS")
    print("=" * 60)
    
    configs = {
        "High Sensitivity": create_high_sensitivity_config(),
        "Low Quality Scan": create_low_quality_config(),
        "Page Contour Only": create_page_contour_config()
    }
    
    for config_name, config in configs.items():
        print(f"\n🔄 Trying configuration: {config_name}")
        
        rectifier = DocumentRectifier(config)
        result = rectifier.process_document(
            image_path=image_path,
            output_dir=os.path.join(output_dir, config_name.lower().replace(" ", "_")),
            save_intermediate=False
        )
        
        if result.success:
            print(f"✅ Success with {config_name}!")
            print(f"   • Markers: {result.quality.marker_count}/4")
            print(f"   • Method: {result.debug.detection_method}")
            print(f"   • Skew residual: {result.quality.skew_residual_deg:.3f}°")
            return True
        else:
            print(f"❌ Failed with {config_name}: {result.error_message}")
    
    return False


def create_high_sensitivity_config():
    """Configuration with very sensitive marker detection."""
    config = Config()
    config.marker.min_area_ratio = 0.00005  # Very small markers
    config.marker.square_ratio_min = 0.5    # Very tolerant shape
    config.clahe.clip = 3.5                 # High contrast
    config.fallback.page_contour = True
    return config


def create_low_quality_config():
    """Configuration for low quality/blurry scans."""
    config = Config()
    config.illumination.morph_k = 61        # Strong background removal
    config.clahe.clip = 4.0                 # Very high contrast
    config.denoise.d = 9                    # Strong denoising
    config.unsharp.amount = 2.0             # Strong sharpening
    config.marker.min_area_ratio = 0.0001
    config.fallback.page_contour = True
    return config


def create_page_contour_config():
    """Configuration that relies on page contour detection."""
    config = Config()
    config.marker.min_area_ratio = 0.01     # Impossible to find markers
    config.fallback.page_contour = True     # Force page contour method
    return config


def main():
    """Main function."""
    setup_logging()
    
    # Image path
    image_path = r"C:\Users\<USER>\Downloads\ptntest.jpg"
    
    # Test with primary configuration
    success = test_image_processing(image_path)
    
    # If failed, try alternative configurations
    if not success:
        print("\n⚠️  Primary configuration failed. Trying alternatives...")
        try_alternative_configs(image_path)
    
    print(f"\n📁 Check output directory 'ptntest_output' for results")
    print(f"📄 Check 'ptntest_processing.log' for detailed logs")


if __name__ == "__main__":
    main()
