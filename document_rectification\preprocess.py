"""
Preprocessing module for document rectification.

Handles noise reduction, illumination normalization, contrast enhancement,
and sharpening to prepare images for marker detection and processing.
"""

import cv2
import numpy as np
from typing import Tu<PERSON>, Dict, Any

from .types import Config, DenoiseConfig, CLAHEConfig, UnsharpConfig, IlluminationConfig


def to_gray(image: np.ndarray) -> np.ndarray:
    """Convert image to grayscale if needed."""
    if len(image.shape) == 3:
        return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    return image.copy()


def denoise_image(image: np.ndarray, config: DenoiseConfig) -> np.ndarray:
    """
    Apply denoising filter to reduce noise while preserving edges.
    
    Args:
        image: Input grayscale image
        config: Denoising configuration
        
    Returns:
        Denoised image
    """
    if config.method == "bilateral":
        return cv2.bilateralFilter(
            image, 
            config.d, 
            config.sigmaC, 
            config.sigmaS
        )
    elif config.method == "gaussian":
        return cv2.GaussianBlur(image, (5, 5), 1.0)
    else:
        return image.copy()


def normalize_illumination(image: np.ndarray, config: IlluminationConfig) -> np.ndarray:
    """
    Normalize illumination using morphological background estimation.
    
    Args:
        image: Input grayscale image
        config: Illumination configuration
        
    Returns:
        Illumination-normalized image
    """
    # Create morphological kernel
    kernel = cv2.getStructuringElement(
        cv2.MORPH_ELLIPSE, 
        (config.morph_k, config.morph_k)
    )
    
    # Estimate background using morphological opening
    background = cv2.morphologyEx(image, cv2.MORPH_OPEN, kernel)
    
    # Avoid division by zero
    background = np.maximum(background, 1)
    
    # Normalize: (image / background) * 255
    normalized = (image.astype(np.float32) / background.astype(np.float32)) * 255.0
    normalized = np.clip(normalized, 0, 255).astype(np.uint8)
    
    return normalized


def apply_clahe(image: np.ndarray, config: CLAHEConfig) -> np.ndarray:
    """
    Apply Contrast Limited Adaptive Histogram Equalization.
    
    Args:
        image: Input grayscale image
        config: CLAHE configuration
        
    Returns:
        CLAHE-enhanced image
    """
    clahe = cv2.createCLAHE(
        clipLimit=config.clip,
        tileGridSize=config.tile
    )
    return clahe.apply(image)


def unsharp_mask(image: np.ndarray, config: UnsharpConfig) -> np.ndarray:
    """
    Apply unsharp masking for edge enhancement.
    
    Args:
        image: Input grayscale image
        config: Unsharp masking configuration
        
    Returns:
        Sharpened image
    """
    # Create Gaussian blur
    blurred = cv2.GaussianBlur(image, (0, 0), config.sigma)
    
    # Calculate unsharp mask
    mask = image.astype(np.float32) - blurred.astype(np.float32)
    
    # Apply sharpening
    sharpened = image.astype(np.float32) + config.amount * mask
    sharpened = np.clip(sharpened, 0, 255).astype(np.uint8)
    
    return sharpened


def preprocess_image(image: np.ndarray, config: Config) -> Tuple[np.ndarray, np.ndarray, Dict[str, Any]]:
    """
    Complete preprocessing pipeline for document images.
    
    Args:
        image: Input image (color or grayscale)
        config: Complete configuration
        
    Returns:
        Tuple of (enhanced_gray, original_color, stats)
        - enhanced_gray: Preprocessed grayscale image for detection
        - original_color: Original color image for final warping
        - stats: Processing statistics for debugging
    """
    stats = {}
    
    # Keep original color image for final warping
    original_color = image.copy()
    
    # Convert to grayscale
    gray = to_gray(image)
    stats['original_shape'] = gray.shape
    stats['original_mean'] = float(np.mean(gray))
    stats['original_std'] = float(np.std(gray))
    
    # Step 1: Denoise
    denoised = denoise_image(gray, config.denoise)
    stats['denoise_method'] = config.denoise.method
    
    # Step 2: Normalize illumination
    normalized = normalize_illumination(denoised, config.illumination)
    stats['illumination_mean'] = float(np.mean(normalized))
    stats['illumination_std'] = float(np.std(normalized))
    
    # Step 3: Apply CLAHE
    clahe_enhanced = apply_clahe(normalized, config.clahe)
    stats['clahe_clip'] = config.clahe.clip
    stats['clahe_mean'] = float(np.mean(clahe_enhanced))
    
    # Step 4: Unsharp masking
    sharpened = unsharp_mask(clahe_enhanced, config.unsharp)
    stats['unsharp_sigma'] = config.unsharp.sigma
    stats['unsharp_amount'] = config.unsharp.amount
    stats['final_mean'] = float(np.mean(sharpened))
    stats['final_std'] = float(np.std(sharpened))
    
    return sharpened, original_color, stats


def calculate_edge_strength(image: np.ndarray) -> float:
    """
    Calculate edge strength as a quality metric.
    
    Args:
        image: Input grayscale image
        
    Returns:
        Edge strength value (0-1)
    """
    # Apply Canny edge detection
    edges = cv2.Canny(image, 50, 150)
    
    # Calculate edge strength as ratio of edge pixels
    edge_strength = np.mean(edges) / 255.0
    
    return float(edge_strength)
