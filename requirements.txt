# Document Rectification System Requirements
# Core dependencies for OMR paper processing

# Computer Vision and Image Processing
opencv-python>=4.5.0
numpy>=1.19.0

# Optional: Enhanced image processing (uncomment if needed)
# scikit-image>=0.18.0
# scipy>=1.6.0

# Development and Testing
# pytest>=6.0.0
# pytest-cov>=2.10.0

# Documentation (uncomment if building docs)
# sphinx>=4.0.0
# sphinx-rtd-theme>=0.5.0

# Jupyter notebooks for examples (uncomment if needed)
# jupyter>=1.0.0
# matplotlib>=3.3.0
