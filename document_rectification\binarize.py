"""
Binarization module for document rectification.

Handles adaptive thresholding and noise filtering to create clean
binary images suitable for OMR reading and content analysis.
"""

import cv2
import numpy as np
from typing import <PERSON><PERSON>, Dict, Any

from .types import Config, AdaptiveConfig, BinarizeConfig, IlluminationConfig
from .preprocess import normalize_illumination


def adaptive_threshold(image: np.ndarray, config: AdaptiveConfig) -> np.ndarray:
    """
    Apply adaptive thresholding for binarization.
    
    Args:
        image: Input grayscale image
        config: Adaptive thresholding configuration
        
    Returns:
        Binary image (0 and 255 values)
    """
    # Determine adaptive method
    if config.method.lower() == "gaussian":
        adaptive_method = cv2.ADAPTIVE_THRESH_GAUSSIAN_C
    else:  # mean
        adaptive_method = cv2.ADAPTIVE_THRESH_MEAN_C
    
    # Apply adaptive thresholding
    # Using THRESH_BINARY_INV so text/marks are white (255) on black (0) background
    binary = cv2.adaptiveThreshold(
        image,
        255,                           # Max value
        adaptive_method,               # Adaptive method
        cv2.THRESH_BINARY_INV,        # Threshold type (inverted)
        config.block,                  # Block size
        config.C                       # Constant subtracted from mean
    )
    
    return binary


def remove_noise(binary_image: np.ndarray, config: BinarizeConfig) -> np.ndarray:
    """
    Remove salt-and-pepper noise from binary image.
    
    Args:
        binary_image: Input binary image
        config: Binarization configuration
        
    Returns:
        Denoised binary image
    """
    # Apply median blur to remove salt-and-pepper noise
    if config.median_blur > 1:
        denoised = cv2.medianBlur(binary_image, config.median_blur)
    else:
        denoised = binary_image.copy()
    
    return denoised


def morphological_cleanup(binary_image: np.ndarray, kernel_size: int = 3) -> np.ndarray:
    """
    Apply morphological operations to clean up binary image.
    
    Args:
        binary_image: Input binary image
        kernel_size: Size of morphological kernel
        
    Returns:
        Cleaned binary image
    """
    # Create morphological kernel
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
    
    # Apply opening (erosion followed by dilation) to remove small noise
    opened = cv2.morphologyEx(binary_image, cv2.MORPH_OPEN, kernel)
    
    # Apply closing (dilation followed by erosion) to fill small gaps
    closed = cv2.morphologyEx(opened, cv2.MORPH_CLOSE, kernel)
    
    return closed


def enhance_contrast_before_binarization(image: np.ndarray) -> np.ndarray:
    """
    Enhance contrast before binarization for better results.
    
    Args:
        image: Input grayscale image
        
    Returns:
        Contrast-enhanced image
    """
    # Apply histogram equalization
    equalized = cv2.equalizeHist(image)
    
    # Blend with original image (50-50 mix)
    enhanced = cv2.addWeighted(image, 0.5, equalized, 0.5, 0)
    
    return enhanced


def otsu_threshold(image: np.ndarray) -> Tuple[np.ndarray, float]:
    """
    Apply Otsu's automatic thresholding.
    
    Args:
        image: Input grayscale image
        
    Returns:
        Tuple of (binary_image, threshold_value)
    """
    threshold_value, binary = cv2.threshold(
        image, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU
    )
    
    return binary, threshold_value


def binarize_document(image: np.ndarray, config: Config) -> Tuple[np.ndarray, Dict[str, Any]]:
    """
    Complete binarization pipeline for document images.
    
    Args:
        image: Input image (should be already warped and deskewed)
        config: Complete configuration
        
    Returns:
        Tuple of (binary_image, processing_stats)
    """
    stats = {}
    
    # Convert to grayscale if needed
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()
    
    stats['input_shape'] = gray.shape
    stats['input_mean'] = float(np.mean(gray))
    stats['input_std'] = float(np.std(gray))
    
    # Step 1: Normalize illumination again (after warping)
    normalized = normalize_illumination(gray, config.illumination)
    stats['normalized_mean'] = float(np.mean(normalized))
    
    # Step 2: Enhance contrast
    enhanced = enhance_contrast_before_binarization(normalized)
    stats['enhanced_mean'] = float(np.mean(enhanced))
    
    # Step 3: Apply adaptive thresholding
    binary = adaptive_threshold(enhanced, config.adaptive)
    stats['adaptive_method'] = config.adaptive.method
    stats['adaptive_block'] = config.adaptive.block
    stats['adaptive_C'] = config.adaptive.C
    
    # Step 4: Remove noise
    denoised = remove_noise(binary, config.binarize)
    stats['median_blur_size'] = config.binarize.median_blur
    
    # Step 5: Morphological cleanup (optional, light touch)
    cleaned = morphological_cleanup(denoised, kernel_size=2)
    
    # Calculate final statistics
    white_pixels = np.sum(cleaned == 255)
    total_pixels = cleaned.size
    stats['white_pixel_ratio'] = float(white_pixels / total_pixels)
    stats['black_pixel_ratio'] = float(1.0 - stats['white_pixel_ratio'])
    
    return cleaned, stats


def analyze_binarization_quality(binary_image: np.ndarray, original_gray: np.ndarray) -> Dict[str, float]:
    """
    Analyze quality of binarization result.
    
    Args:
        binary_image: Result of binarization
        original_gray: Original grayscale image
        
    Returns:
        Dictionary of quality metrics
    """
    metrics = {}
    
    # Calculate contrast in original image
    original_std = np.std(original_gray)
    metrics['original_contrast'] = float(original_std)
    
    # Calculate edge preservation
    # Apply edge detection to both images
    edges_original = cv2.Canny(original_gray, 50, 150)
    edges_binary = cv2.Canny(binary_image, 50, 150)
    
    # Calculate edge preservation ratio
    original_edges = np.sum(edges_original > 0)
    preserved_edges = np.sum((edges_original > 0) & (edges_binary > 0))
    
    if original_edges > 0:
        metrics['edge_preservation'] = float(preserved_edges / original_edges)
    else:
        metrics['edge_preservation'] = 0.0
    
    # Calculate uniformity of background and foreground
    background_pixels = binary_image[binary_image == 0]
    foreground_pixels = binary_image[binary_image == 255]
    
    metrics['background_uniformity'] = 1.0 if len(background_pixels) > 0 else 0.0
    metrics['foreground_uniformity'] = 1.0 if len(foreground_pixels) > 0 else 0.0
    
    # Calculate overall quality score (0-1)
    quality_score = (
        metrics['edge_preservation'] * 0.4 +
        min(metrics['original_contrast'] / 50.0, 1.0) * 0.3 +
        metrics['background_uniformity'] * 0.15 +
        metrics['foreground_uniformity'] * 0.15
    )
    metrics['overall_quality'] = float(min(quality_score, 1.0))
    
    return metrics


def create_visualization(original: np.ndarray, binary: np.ndarray) -> np.ndarray:
    """
    Create side-by-side visualization of original and binary images.
    
    Args:
        original: Original grayscale image
        binary: Binary result
        
    Returns:
        Combined visualization image
    """
    # Ensure both images have same height
    h1, w1 = original.shape
    h2, w2 = binary.shape
    
    if h1 != h2:
        # Resize to match heights
        target_height = min(h1, h2)
        original = cv2.resize(original, (int(w1 * target_height / h1), target_height))
        binary = cv2.resize(binary, (int(w2 * target_height / h2), target_height))
    
    # Convert binary to 3-channel for concatenation
    if len(original.shape) == 2:
        original_3ch = cv2.cvtColor(original, cv2.COLOR_GRAY2BGR)
    else:
        original_3ch = original.copy()
    
    binary_3ch = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
    
    # Concatenate horizontally
    combined = np.hstack([original_3ch, binary_3ch])
    
    return combined
