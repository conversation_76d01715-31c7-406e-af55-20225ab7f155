"""
Quality metrics module for document rectification.

Provides comprehensive quality assessment including skew residual,
edge strength, marker detection success, and overall processing quality.
"""

import cv2
import numpy as np
from typing import Dict, Any, Optional, Tuple
import math

from .types import Config, QualityMetrics, HoughConfig
from .geometry import detect_skew_hough, calculate_skew_residual
from .preprocess import calculate_edge_strength


def calculate_marker_quality(corners: Optional[np.ndarray], detection_method: str, image_shape: Tuple[int, int]) -> Dict[str, Any]:
    """
    Calculate quality metrics related to marker/corner detection.
    
    Args:
        corners: Detected corner points (or None if failed)
        detection_method: Method used for detection
        image_shape: Shape of input image
        
    Returns:
        Dictionary of marker quality metrics
    """
    metrics = {
        'detection_success': corners is not None,
        'detection_method': detection_method,
        'marker_count': 4 if corners is not None else 0,
        'corner_quality': 0.0,
        'quadrilateral_regularity': 0.0
    }
    
    if corners is None:
        return metrics
    
    h, w = image_shape
    
    # Calculate corner quality based on position
    # Good corners should be near image corners but not at edges
    expected_corners = np.array([
        [w * 0.1, h * 0.1],    # Top-left
        [w * 0.9, h * 0.1],    # Top-right  
        [w * 0.9, h * 0.9],    # Bottom-right
        [w * 0.1, h * 0.9]     # Bottom-left
    ])
    
    # Calculate distances from expected positions
    distances = []
    for i, corner in enumerate(corners):
        dist = np.linalg.norm(corner - expected_corners[i])
        normalized_dist = dist / math.sqrt(w*w + h*h)  # Normalize by diagonal
        distances.append(normalized_dist)
    
    # Corner quality: lower distance = higher quality
    avg_distance = np.mean(distances)
    metrics['corner_quality'] = float(max(0.0, 1.0 - avg_distance * 5))  # Scale factor
    
    # Calculate quadrilateral regularity
    # Measure how close the shape is to a rectangle
    tl, tr, br, bl = corners
    
    # Calculate side lengths
    top_length = np.linalg.norm(tr - tl)
    right_length = np.linalg.norm(br - tr)
    bottom_length = np.linalg.norm(bl - br)
    left_length = np.linalg.norm(tl - bl)
    
    # Calculate aspect ratio consistency
    width_avg = (top_length + bottom_length) / 2
    height_avg = (left_length + right_length) / 2
    
    if width_avg > 0 and height_avg > 0:
        width_consistency = 1.0 - abs(top_length - bottom_length) / width_avg
        height_consistency = 1.0 - abs(left_length - right_length) / height_avg
        metrics['quadrilateral_regularity'] = float((width_consistency + height_consistency) / 2)
    
    return metrics


def calculate_perspective_quality(H: Optional[np.ndarray], src_corners: Optional[np.ndarray], target_size: Tuple[int, int]) -> Dict[str, Any]:
    """
    Calculate quality metrics for perspective transformation.
    
    Args:
        H: Homography matrix
        src_corners: Source corner points
        target_size: Target dimensions (width, height)
        
    Returns:
        Dictionary of perspective quality metrics
    """
    metrics = {
        'homography_valid': H is not None,
        'transformation_quality': 0.0,
        'area_preservation': 0.0,
        'aspect_ratio_error': 0.0
    }
    
    if H is None or src_corners is None:
        return metrics
    
    # Check homography condition number (lower is better)
    try:
        cond_number = np.linalg.cond(H)
        metrics['transformation_quality'] = float(max(0.0, 1.0 - cond_number / 1000.0))
    except:
        metrics['transformation_quality'] = 0.0
    
    # Calculate area preservation
    # Compare area of source quadrilateral to target area
    src_area = cv2.contourArea(src_corners.reshape(-1, 1, 2))
    target_area = target_size[0] * target_size[1]
    
    if target_area > 0:
        area_ratio = src_area / target_area
        # Good preservation should have ratio close to 1.0
        metrics['area_preservation'] = float(1.0 - abs(1.0 - area_ratio))
    
    # Calculate aspect ratio error
    tl, tr, br, bl = src_corners
    src_width = (np.linalg.norm(tr - tl) + np.linalg.norm(br - bl)) / 2
    src_height = (np.linalg.norm(bl - tl) + np.linalg.norm(br - tr)) / 2
    
    if src_height > 0:
        src_aspect = src_width / src_height
        target_aspect = target_size[0] / target_size[1]
        aspect_error = abs(src_aspect - target_aspect) / target_aspect
        metrics['aspect_ratio_error'] = float(min(1.0, aspect_error))
    
    return metrics


def calculate_skew_quality(image: np.ndarray, applied_angle: float, config: HoughConfig) -> Dict[str, Any]:
    """
    Calculate quality metrics for skew correction.
    
    Args:
        image: Corrected image
        applied_angle: Angle that was applied for correction
        config: Hough configuration for residual calculation
        
    Returns:
        Dictionary of skew quality metrics
    """
    metrics = {
        'applied_angle_deg': float(applied_angle),
        'skew_residual_deg': 0.0,
        'correction_quality': 0.0
    }
    
    # Calculate residual skew
    residual = calculate_skew_residual(image, config)
    metrics['skew_residual_deg'] = float(residual)
    
    # Quality is better when residual is smaller
    # Good correction should have residual < 0.5 degrees
    if residual < 0.5:
        metrics['correction_quality'] = 1.0
    elif residual < 1.0:
        metrics['correction_quality'] = float(1.0 - (residual - 0.5) * 2)
    else:
        metrics['correction_quality'] = 0.0
    
    return metrics


def calculate_image_quality(original: np.ndarray, processed: np.ndarray) -> Dict[str, Any]:
    """
    Calculate overall image quality metrics.
    
    Args:
        original: Original input image
        processed: Final processed image
        
    Returns:
        Dictionary of image quality metrics
    """
    metrics = {
        'edge_strength': 0.0,
        'contrast_improvement': 0.0,
        'noise_reduction': 0.0,
        'sharpness': 0.0
    }
    
    # Convert to grayscale if needed
    if len(original.shape) == 3:
        orig_gray = cv2.cvtColor(original, cv2.COLOR_BGR2GRAY)
    else:
        orig_gray = original.copy()
    
    if len(processed.shape) == 3:
        proc_gray = cv2.cvtColor(processed, cv2.COLOR_BGR2GRAY)
    else:
        proc_gray = processed.copy()
    
    # Calculate edge strength
    metrics['edge_strength'] = calculate_edge_strength(proc_gray)
    
    # Calculate contrast improvement
    orig_std = np.std(orig_gray)
    proc_std = np.std(proc_gray)
    
    if orig_std > 0:
        contrast_ratio = proc_std / orig_std
        metrics['contrast_improvement'] = float(min(2.0, contrast_ratio) / 2.0)  # Normalize to 0-1
    
    # Calculate noise reduction (using local variance)
    orig_noise = calculate_local_variance(orig_gray)
    proc_noise = calculate_local_variance(proc_gray)
    
    if orig_noise > 0:
        noise_reduction = max(0.0, (orig_noise - proc_noise) / orig_noise)
        metrics['noise_reduction'] = float(noise_reduction)
    
    # Calculate sharpness using Laplacian variance
    laplacian = cv2.Laplacian(proc_gray, cv2.CV_64F)
    sharpness = np.var(laplacian)
    metrics['sharpness'] = float(min(sharpness / 1000.0, 1.0))  # Normalize
    
    return metrics


def calculate_local_variance(image: np.ndarray, kernel_size: int = 5) -> float:
    """
    Calculate local variance as a noise measure.
    
    Args:
        image: Input grayscale image
        kernel_size: Size of local window
        
    Returns:
        Average local variance
    """
    # Calculate local mean
    kernel = np.ones((kernel_size, kernel_size), np.float32) / (kernel_size * kernel_size)
    local_mean = cv2.filter2D(image.astype(np.float32), -1, kernel)
    
    # Calculate local variance
    local_variance = cv2.filter2D((image.astype(np.float32) - local_mean) ** 2, -1, kernel)
    
    return float(np.mean(local_variance))


def calculate_binarization_quality(binary_image: np.ndarray, original_gray: np.ndarray) -> Dict[str, Any]:
    """
    Calculate quality metrics for binarization result.
    
    Args:
        binary_image: Binary result
        original_gray: Original grayscale image
        
    Returns:
        Dictionary of binarization quality metrics
    """
    metrics = {
        'foreground_ratio': 0.0,
        'connectivity': 0.0,
        'uniformity': 0.0,
        'edge_preservation': 0.0
    }
    
    # Calculate foreground ratio
    white_pixels = np.sum(binary_image == 255)
    total_pixels = binary_image.size
    metrics['foreground_ratio'] = float(white_pixels / total_pixels)
    
    # Calculate connectivity (using connected components)
    num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(binary_image)
    # Good binarization should have reasonable number of components
    # Too many = noisy, too few = over-smoothed
    if 10 <= num_labels <= 1000:
        metrics['connectivity'] = 1.0
    else:
        metrics['connectivity'] = float(max(0.0, 1.0 - abs(math.log10(num_labels) - 2.0)))
    
    # Calculate uniformity of regions
    if num_labels > 1:
        # Calculate variance of component sizes
        component_sizes = stats[1:, cv2.CC_STAT_AREA]  # Skip background
        if len(component_sizes) > 0:
            size_variance = np.var(component_sizes)
            mean_size = np.mean(component_sizes)
            if mean_size > 0:
                cv_size = size_variance / (mean_size ** 2)  # Coefficient of variation
                metrics['uniformity'] = float(max(0.0, 1.0 - cv_size))
    
    # Calculate edge preservation
    orig_edges = cv2.Canny(original_gray, 50, 150)
    bin_edges = cv2.Canny(binary_image, 50, 150)
    
    orig_edge_pixels = np.sum(orig_edges > 0)
    preserved_edges = np.sum((orig_edges > 0) & (bin_edges > 0))
    
    if orig_edge_pixels > 0:
        metrics['edge_preservation'] = float(preserved_edges / orig_edge_pixels)
    
    return metrics


def compile_quality_metrics(
    corners: Optional[np.ndarray],
    detection_method: str,
    H: Optional[np.ndarray],
    applied_angle: float,
    original_image: np.ndarray,
    warped_image: np.ndarray,
    binary_image: np.ndarray,
    config: Config
) -> QualityMetrics:
    """
    Compile comprehensive quality metrics for the entire pipeline.
    
    Args:
        corners: Detected corner points
        detection_method: Corner detection method used
        H: Homography matrix
        applied_angle: Skew correction angle applied
        original_image: Original input image
        warped_image: Perspective-corrected image
        binary_image: Final binary image
        config: Processing configuration
        
    Returns:
        QualityMetrics object with all calculated metrics
    """
    # Calculate individual metric groups
    marker_metrics = calculate_marker_quality(corners, detection_method, original_image.shape[:2])
    
    target_size = (config.target_size.width, config.target_size.height)
    perspective_metrics = calculate_perspective_quality(H, corners, target_size)
    
    skew_metrics = calculate_skew_quality(warped_image, applied_angle, config.hough)
    
    # Convert warped to grayscale for quality calculation
    if len(warped_image.shape) == 3:
        warped_gray = cv2.cvtColor(warped_image, cv2.COLOR_BGR2GRAY)
    else:
        warped_gray = warped_image.copy()
    
    image_metrics = calculate_image_quality(original_image, warped_gray)
    binarization_metrics = calculate_binarization_quality(binary_image, warped_gray)
    
    # Create final quality metrics
    quality = QualityMetrics(
        marker_count=marker_metrics['marker_count'],
        skew_residual_deg=skew_metrics['skew_residual_deg'],
        edge_strength=image_metrics['edge_strength']
    )
    
    return quality
