"""
Main pipeline orchestrator for document rectification.

Coordinates all processing modules to provide complete document rectification
from input image to final warped and binarized output.
"""

import cv2
import numpy as np
import os
from typing import Optional, Tuple, Dict, Any
import logging

from .types import Config, Result, DebugInfo, QualityMetrics, NotEnoughCornersError, ProcessingError
from .preprocess import preprocess_image
from .markers import detect_corners, validate_corners
from .geometry import warp_perspective, deskew_hough, validate_homography
from .binarize import binarize_document
from .metrics import compile_quality_metrics
from .align import fine_align_document


class DocumentRectifier:
    """
    Main class for document rectification pipeline.
    
    Handles the complete workflow from input image to rectified output,
    including preprocessing, corner detection, perspective correction,
    deskewing, and binarization.
    """
    
    def __init__(self, config: Optional[Config] = None):
        """
        Initialize the document rectifier.
        
        Args:
            config: Configuration object. If None, uses default configuration.
        """
        self.config = config or Config()
        self.logger = logging.getLogger(__name__)
        
    def process_document(
        self, 
        image_path: str, 
        output_dir: str = "output",
        save_intermediate: bool = False
    ) -> Result:
        """
        Process a document image through the complete rectification pipeline.
        
        Args:
            image_path: Path to input image
            output_dir: Directory to save output files
            save_intermediate: Whether to save intermediate processing steps
            
        Returns:
            Result object containing all processing information
        """
        result = Result()
        
        try:
            # Create output directory
            os.makedirs(output_dir, exist_ok=True)
            
            # Load input image
            image = cv2.imread(image_path)
            if image is None:
                raise ProcessingError(f"Could not load image: {image_path}")
            
            self.logger.info(f"Processing image: {image_path}")
            self.logger.info(f"Input image shape: {image.shape}")
            
            # Step 1: Preprocessing
            self.logger.info("Step 1: Preprocessing...")
            enhanced_gray, original_color, preprocess_stats = preprocess_image(image, self.config)
            
            if save_intermediate:
                cv2.imwrite(os.path.join(output_dir, "01_preprocessed.jpg"), enhanced_gray)
            
            # Step 2: Corner/Marker Detection
            self.logger.info("Step 2: Corner detection...")
            corners, detection_method = detect_corners(enhanced_gray, self.config)
            
            if corners is None:
                raise NotEnoughCornersError(
                    "Could not detect 4 corners. Try adjusting preprocessing parameters or marker detection settings."
                )
            
            # Validate corners
            if not validate_corners(corners, enhanced_gray.shape):
                raise NotEnoughCornersError("Detected corners are invalid or outside image bounds.")
            
            self.logger.info(f"Corners detected using method: {detection_method}")
            
            # Step 3: Perspective Correction
            self.logger.info("Step 3: Perspective correction...")
            target_size = self.config.target_size
            warped_color, H = warp_perspective(original_color, corners, target_size)
            
            # Validate homography
            if not validate_homography(H, image.shape[:2], target_size):
                raise ProcessingError("Invalid homography matrix generated.")
            
            if save_intermediate:
                cv2.imwrite(os.path.join(output_dir, "02_warped.jpg"), warped_color)
            
            # Step 4: Deskewing
            self.logger.info("Step 4: Deskewing...")
            deskewed_color, skew_angle = deskew_hough(warped_color, self.config.hough)
            
            self.logger.info(f"Applied skew correction: {skew_angle:.2f} degrees")
            
            if save_intermediate:
                cv2.imwrite(os.path.join(output_dir, "03_deskewed.jpg"), deskewed_color)
            
            # Step 5: Fine Alignment (optional)
            aligned_color = deskewed_color
            alignment_stats = {}
            
            if self.config.fallback.tps_enable:
                self.logger.info("Step 5: Fine alignment...")
                aligned_color, alignment_stats = fine_align_document(
                    deskewed_color, None, self.config
                )
                
                if alignment_stats.get('alignment_applied', False):
                    self.logger.info("Fine alignment applied")
                    if save_intermediate:
                        cv2.imwrite(os.path.join(output_dir, "04_aligned.jpg"), aligned_color)
            
            # Step 6: Binarization
            self.logger.info("Step 6: Binarization...")
            binary_image, binarization_stats = binarize_document(aligned_color, self.config)
            
            if save_intermediate:
                cv2.imwrite(os.path.join(output_dir, "05_binary.jpg"), binary_image)
            
            # Save final outputs
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            warped_path = os.path.join(output_dir, f"{base_name}_warped.jpg")
            binary_path = os.path.join(output_dir, f"{base_name}_binary.jpg")
            
            cv2.imwrite(warped_path, aligned_color)
            cv2.imwrite(binary_path, binary_image)
            
            # Step 7: Quality Assessment
            self.logger.info("Step 7: Quality assessment...")
            quality_metrics = compile_quality_metrics(
                corners=corners,
                detection_method=detection_method,
                H=H,
                applied_angle=skew_angle,
                original_image=image,
                warped_image=aligned_color,
                binary_image=binary_image,
                config=self.config
            )
            
            # Compile debug information
            debug_info = DebugInfo(
                corners_src=[(float(p[0]), float(p[1])) for p in corners],
                corners_dst=[
                    (0.0, 0.0),
                    (float(target_size.width - 1), 0.0),
                    (float(target_size.width - 1), float(target_size.height - 1)),
                    (0.0, float(target_size.height - 1))
                ],
                detection_method=detection_method,
                preprocessing_stats={
                    **preprocess_stats,
                    **binarization_stats,
                    **alignment_stats
                }
            )
            
            # Compile final result
            result = Result(
                warped_path=warped_path,
                binarized_path=binary_path,
                angle_deg=float(skew_angle),
                H_3x3=H,
                quality=quality_metrics,
                debug=debug_info,
                success=True,
                error_message=""
            )
            
            self.logger.info("Document rectification completed successfully")
            self.logger.info(f"Quality metrics - Markers: {quality_metrics.marker_count}, "
                           f"Skew residual: {quality_metrics.skew_residual_deg:.2f}°, "
                           f"Edge strength: {quality_metrics.edge_strength:.3f}")
            
            return result
            
        except NotEnoughCornersError as e:
            self.logger.error(f"Corner detection failed: {e}")
            result.success = False
            result.error_message = str(e)
            return result
            
        except ProcessingError as e:
            self.logger.error(f"Processing error: {e}")
            result.success = False
            result.error_message = str(e)
            return result
            
        except Exception as e:
            self.logger.error(f"Unexpected error: {e}")
            result.success = False
            result.error_message = f"Unexpected error: {str(e)}"
            return result
    
    def process_image_array(
        self, 
        image: np.ndarray, 
        output_dir: str = "output",
        base_name: str = "document"
    ) -> Result:
        """
        Process a document image from numpy array.
        
        Args:
            image: Input image as numpy array
            output_dir: Directory to save output files
            base_name: Base name for output files
            
        Returns:
            Result object containing all processing information
        """
        # Save temporary image file
        temp_path = os.path.join(output_dir, f"{base_name}_temp.jpg")
        os.makedirs(output_dir, exist_ok=True)
        cv2.imwrite(temp_path, image)
        
        try:
            # Process using file path method
            result = self.process_document(temp_path, output_dir)
            
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.remove(temp_path)
            
            return result
            
        except Exception as e:
            # Clean up temporary file on error
            if os.path.exists(temp_path):
                os.remove(temp_path)
            raise e
    
    def batch_process(
        self, 
        input_dir: str, 
        output_dir: str,
        file_extensions: Tuple[str, ...] = ('.jpg', '.jpeg', '.png', '.tiff', '.bmp')
    ) -> Dict[str, Result]:
        """
        Process multiple documents in a directory.
        
        Args:
            input_dir: Directory containing input images
            output_dir: Directory to save output files
            file_extensions: Tuple of allowed file extensions
            
        Returns:
            Dictionary mapping file names to Result objects
        """
        results = {}
        
        # Find all image files
        image_files = []
        for ext in file_extensions:
            pattern = os.path.join(input_dir, f"*{ext}")
            import glob
            image_files.extend(glob.glob(pattern))
        
        self.logger.info(f"Found {len(image_files)} images to process")
        
        # Process each image
        for i, image_path in enumerate(image_files):
            file_name = os.path.basename(image_path)
            self.logger.info(f"Processing {i+1}/{len(image_files)}: {file_name}")
            
            try:
                result = self.process_document(image_path, output_dir)
                results[file_name] = result
                
                if result.success:
                    self.logger.info(f"✓ Successfully processed {file_name}")
                else:
                    self.logger.warning(f"✗ Failed to process {file_name}: {result.error_message}")
                    
            except Exception as e:
                self.logger.error(f"✗ Error processing {file_name}: {e}")
                results[file_name] = Result(
                    success=False,
                    error_message=str(e)
                )
        
        return results

    def get_processing_summary(self, results: Dict[str, Result]) -> Dict[str, Any]:
        """
        Generate summary statistics for batch processing results.

        Args:
            results: Dictionary of processing results

        Returns:
            Summary statistics dictionary
        """
        total_files = len(results)
        successful = sum(1 for r in results.values() if r.success)
        failed = total_files - successful

        # Calculate average quality metrics for successful results
        successful_results = [r for r in results.values() if r.success]

        if successful_results:
            avg_skew_residual = np.mean([r.quality.skew_residual_deg for r in successful_results])
            avg_edge_strength = np.mean([r.quality.edge_strength for r in successful_results])
            marker_detection_rate = np.mean([1.0 if r.quality.marker_count == 4 else 0.0 for r in successful_results])
        else:
            avg_skew_residual = 0.0
            avg_edge_strength = 0.0
            marker_detection_rate = 0.0

        return {
            'total_files': total_files,
            'successful': successful,
            'failed': failed,
            'success_rate': successful / total_files if total_files > 0 else 0.0,
            'avg_skew_residual_deg': float(avg_skew_residual),
            'avg_edge_strength': float(avg_edge_strength),
            'marker_detection_rate': float(marker_detection_rate)
        }
