#!/usr/bin/env python3
"""
Example usage script for Document Rectification System.

Demonstrates how to use the system for OMR paper rectification
with various configuration options and processing modes.
"""

import os
import sys
import logging
import json
import argparse
from pathlib import Path

# Add the document_rectification module to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from document_rectification import DocumentRectifier, Config
from document_rectification.types import TargetSize, CLAHEConfig, MarkerConfig


def setup_logging(level=logging.INFO):
    """Setup logging configuration."""
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('document_rectification.log')
        ]
    )


def create_custom_config():
    """Create a custom configuration for OMR papers."""
    config = Config()
    
    # Adjust for A4 OMR papers at 300 DPI
    config.target_size = TargetSize(width=2480, height=3508)
    
    # Enhanced CLAHE for better contrast on exam papers
    config.clahe.clip = 2.5
    config.clahe.tile = (8, 8)
    
    # More sensitive marker detection for small corner markers
    config.marker.min_area_ratio = 0.0001  # Smaller markers
    config.marker.square_ratio_min = 0.7   # Allow slightly rectangular markers
    
    # Enable fallback options
    config.fallback.page_contour = True
    config.fallback.tps_enable = False  # Disable TPS unless needed
    
    return config


def process_single_image(image_path: str, output_dir: str = "output"):
    """
    Process a single OMR image.
    
    Args:
        image_path: Path to input image
        output_dir: Output directory
    """
    print(f"\n=== Processing Single Image ===")
    print(f"Input: {image_path}")
    print(f"Output: {output_dir}")
    
    # Create rectifier with custom config
    config = create_custom_config()
    rectifier = DocumentRectifier(config)
    
    # Process the document
    result = rectifier.process_document(
        image_path=image_path,
        output_dir=output_dir,
        save_intermediate=True  # Save all processing steps
    )
    
    # Print results
    if result.success:
        print("✓ Processing successful!")
        print(f"  Warped image: {result.warped_path}")
        print(f"  Binary image: {result.binarized_path}")
        print(f"  Skew correction: {result.angle_deg:.2f}°")
        print(f"  Quality metrics:")
        print(f"    - Markers detected: {result.quality.marker_count}")
        print(f"    - Skew residual: {result.quality.skew_residual_deg:.3f}°")
        print(f"    - Edge strength: {result.quality.edge_strength:.3f}")
        print(f"  Detection method: {result.debug.detection_method}")
    else:
        print("✗ Processing failed!")
        print(f"  Error: {result.error_message}")
    
    return result


def process_batch(input_dir: str, output_dir: str = "batch_output"):
    """
    Process multiple OMR images in batch.
    
    Args:
        input_dir: Directory containing input images
        output_dir: Output directory
    """
    print(f"\n=== Batch Processing ===")
    print(f"Input directory: {input_dir}")
    print(f"Output directory: {output_dir}")
    
    # Create rectifier
    config = create_custom_config()
    rectifier = DocumentRectifier(config)
    
    # Process batch
    results = rectifier.batch_process(input_dir, output_dir)
    
    # Generate summary
    summary = rectifier.get_processing_summary(results)
    
    print(f"\n=== Batch Processing Summary ===")
    print(f"Total files: {summary['total_files']}")
    print(f"Successful: {summary['successful']}")
    print(f"Failed: {summary['failed']}")
    print(f"Success rate: {summary['success_rate']:.1%}")
    print(f"Average skew residual: {summary['avg_skew_residual_deg']:.3f}°")
    print(f"Average edge strength: {summary['avg_edge_strength']:.3f}")
    print(f"Marker detection rate: {summary['marker_detection_rate']:.1%}")
    
    # Save detailed results
    results_file = os.path.join(output_dir, "batch_results.json")
    save_results_to_json(results, summary, results_file)
    print(f"Detailed results saved to: {results_file}")
    
    return results, summary


def save_results_to_json(results, summary, output_file):
    """Save processing results to JSON file."""
    # Convert results to serializable format
    serializable_results = {}
    
    for filename, result in results.items():
        serializable_results[filename] = {
            'success': result.success,
            'error_message': result.error_message,
            'warped_path': result.warped_path,
            'binarized_path': result.binarized_path,
            'angle_deg': result.angle_deg,
            'quality': {
                'marker_count': result.quality.marker_count,
                'skew_residual_deg': result.quality.skew_residual_deg,
                'edge_strength': result.quality.edge_strength
            } if result.success else None,
            'debug': {
                'detection_method': result.debug.detection_method,
                'corners_src': result.debug.corners_src,
                'corners_dst': result.debug.corners_dst
            } if result.success else None
        }
    
    # Combine with summary
    output_data = {
        'summary': summary,
        'results': serializable_results
    }
    
    # Save to file
    with open(output_file, 'w') as f:
        json.dump(output_data, f, indent=2)


def demonstrate_config_options():
    """Demonstrate different configuration options."""
    print(f"\n=== Configuration Options Demo ===")
    
    # Default configuration
    default_config = Config()
    print("Default Configuration:")
    print(f"  Target size: {default_config.target_size.width}x{default_config.target_size.height}")
    print(f"  CLAHE clip: {default_config.clahe.clip}")
    print(f"  Marker min area ratio: {default_config.marker.min_area_ratio}")
    
    # High quality configuration (slower but better results)
    hq_config = Config()
    hq_config.clahe.clip = 3.0
    hq_config.clahe.tile = (16, 16)
    hq_config.marker.min_area_ratio = 0.00005  # Very sensitive
    hq_config.binarize.median_blur = 5
    hq_config.fallback.tps_enable = True
    
    print("\nHigh Quality Configuration:")
    print(f"  CLAHE clip: {hq_config.clahe.clip}")
    print(f"  CLAHE tile: {hq_config.clahe.tile}")
    print(f"  Marker sensitivity: {hq_config.marker.min_area_ratio}")
    print(f"  TPS enabled: {hq_config.fallback.tps_enable}")
    
    # Fast configuration (faster but may miss some details)
    fast_config = Config()
    fast_config.clahe.clip = 1.5
    fast_config.marker.min_area_ratio = 0.001  # Less sensitive
    fast_config.hough.max_lines = 50
    fast_config.fallback.page_contour = False
    
    print("\nFast Configuration:")
    print(f"  CLAHE clip: {fast_config.clahe.clip}")
    print(f"  Marker sensitivity: {fast_config.marker.min_area_ratio}")
    print(f"  Max Hough lines: {fast_config.hough.max_lines}")
    print(f"  Page contour fallback: {fast_config.fallback.page_contour}")


def main():
    """Main function with command line interface."""
    parser = argparse.ArgumentParser(
        description="Document Rectification System - OMR Paper Processing"
    )
    
    parser.add_argument(
        'mode',
        choices=['single', 'batch', 'demo'],
        help='Processing mode'
    )
    
    parser.add_argument(
        '--input',
        required=False,
        help='Input image file (for single mode) or directory (for batch mode)'
    )
    
    parser.add_argument(
        '--output',
        default='output',
        help='Output directory (default: output)'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    setup_logging(log_level)
    
    print("Document Rectification System - OMR Paper Processing")
    print("=" * 60)
    
    if args.mode == 'demo':
        demonstrate_config_options()
        
    elif args.mode == 'single':
        if not args.input:
            print("Error: --input required for single mode")
            return 1
        
        if not os.path.exists(args.input):
            print(f"Error: Input file not found: {args.input}")
            return 1
        
        result = process_single_image(args.input, args.output)
        return 0 if result.success else 1
        
    elif args.mode == 'batch':
        if not args.input:
            print("Error: --input required for batch mode")
            return 1
        
        if not os.path.isdir(args.input):
            print(f"Error: Input directory not found: {args.input}")
            return 1
        
        results, summary = process_batch(args.input, args.output)
        return 0 if summary['failed'] == 0 else 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
