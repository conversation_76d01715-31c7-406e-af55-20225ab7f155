#!/usr/bin/env python3
"""
Manual corner detection for ptntest.jpg when automatic detection fails.
This script provides interactive corner selection and processing.
"""

import os
import sys
import cv2
import numpy as np

# Add the document_rectification module to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from document_rectification import DocumentRectifier, Config
from document_rectification.geometry import warp_perspective, deskew_hough
from document_rectification.binarize import binarize_document
from document_rectification.types import TargetSize, Result, DebugInfo, QualityMetrics


class ManualCornerSelector:
    """Interactive corner selection tool."""
    
    def __init__(self, image):
        self.image = image.copy()
        self.display_image = image.copy()
        self.corners = []
        self.window_name = "Select 4 Corners (TL, TR, BR, BL) - Press 'r' to reset, 'q' to quit"
        
    def mouse_callback(self, event, x, y, flags, param):
        """Handle mouse clicks for corner selection."""
        if event == cv2.EVENT_LBUTTONDOWN:
            if len(self.corners) < 4:
                self.corners.append((x, y))
                
                # Draw the corner
                cv2.circle(self.display_image, (x, y), 10, (0, 255, 0), -1)
                cv2.putText(self.display_image, str(len(self.corners)), 
                           (x + 15, y - 15), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                
                print(f"Corner {len(self.corners)}: ({x}, {y})")
                
                if len(self.corners) == 4:
                    print("All 4 corners selected! Press 'q' to continue or 'r' to reset.")
    
    def select_corners(self):
        """Interactive corner selection."""
        print("=" * 60)
        print("MANUAL CORNER SELECTION")
        print("=" * 60)
        print("Instructions:")
        print("1. Click on Top-Left corner")
        print("2. Click on Top-Right corner") 
        print("3. Click on Bottom-Right corner")
        print("4. Click on Bottom-Left corner")
        print("5. Press 'r' to reset, 'q' when done")
        print()
        
        # Resize image if too large for display
        h, w = self.image.shape[:2]
        if w > 1200 or h > 900:
            scale = min(1200/w, 900/h)
            display_w = int(w * scale)
            display_h = int(h * scale)
            self.display_image = cv2.resize(self.image, (display_w, display_h))
            self.scale_factor = scale
        else:
            self.scale_factor = 1.0
        
        cv2.namedWindow(self.window_name, cv2.WINDOW_NORMAL)
        cv2.setMouseCallback(self.window_name, self.mouse_callback)
        
        while True:
            cv2.imshow(self.window_name, self.display_image)
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord('q'):
                break
            elif key == ord('r'):
                # Reset
                self.corners = []
                self.display_image = cv2.resize(self.image, 
                    (int(self.image.shape[1] * self.scale_factor), 
                     int(self.image.shape[0] * self.scale_factor)))
                print("Reset corners. Start selecting again.")
        
        cv2.destroyAllWindows()
        
        if len(self.corners) == 4:
            # Convert back to original image coordinates
            if self.scale_factor != 1.0:
                self.corners = [(int(x/self.scale_factor), int(y/self.scale_factor)) 
                               for x, y in self.corners]
            
            return np.array(self.corners, dtype=np.float32)
        else:
            return None


def estimate_corners_from_image_bounds(image, margin_ratio=0.05):
    """
    Estimate corners based on image bounds with a margin.
    Useful when the document fills most of the image.
    """
    h, w = image.shape[:2]
    
    # Calculate margins
    margin_x = int(w * margin_ratio)
    margin_y = int(h * margin_ratio)
    
    # Estimate corners with margins
    corners = np.array([
        [margin_x, margin_y],                    # Top-left
        [w - margin_x, margin_y],                # Top-right
        [w - margin_x, h - margin_y],            # Bottom-right
        [margin_x, h - margin_y]                 # Bottom-left
    ], dtype=np.float32)
    
    return corners


def try_different_preprocessing(image):
    """Try different preprocessing approaches to find corners."""
    
    print("🔄 Trying different preprocessing approaches...")
    
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
    h, w = gray.shape
    
    approaches = [
        ("Original", gray),
        ("Histogram Equalization", cv2.equalizeHist(gray)),
        ("Gaussian Blur + Threshold", None),
        ("Morphological Operations", None),
        ("Edge Enhancement", None)
    ]
    
    results = []
    
    for name, processed in approaches:
        if processed is None:
            if name == "Gaussian Blur + Threshold":
                blurred = cv2.GaussianBlur(gray, (5, 5), 0)
                _, processed = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            elif name == "Morphological Operations":
                kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
                processed = cv2.morphologyEx(gray, cv2.MORPH_GRADIENT, kernel)
            elif name == "Edge Enhancement":
                processed = cv2.Laplacian(gray, cv2.CV_8U)
        
        # Find contours
        if name != "Original":
            contours, _ = cv2.findContours(processed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Look for rectangular contours
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 0.01 * w * h:  # At least 1% of image
                    epsilon = 0.02 * cv2.arcLength(contour, True)
                    approx = cv2.approxPolyDP(contour, epsilon, True)
                    
                    if len(approx) == 4:
                        results.append((name, approx.reshape(4, 2).astype(np.float32)))
                        print(f"   ✅ {name}: Found 4-corner contour")
                        break
            else:
                print(f"   ❌ {name}: No suitable contours")
        
        # Save processed image for inspection
        cv2.imwrite(f"diagnostic_output/preprocessing_{name.lower().replace(' ', '_')}.jpg", processed)
    
    return results


def process_with_manual_corners(image_path, output_dir="manual_output"):
    """Process image with manual corner selection."""
    
    print("=" * 60)
    print("MANUAL CORNER PROCESSING")
    print("=" * 60)
    
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        print(f"❌ Could not load image: {image_path}")
        return None
    
    h, w = image.shape[:2]
    print(f"📊 Image: {w}x{h} pixels")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Try automatic corner estimation first
    print("\n🔄 Trying automatic corner estimation...")
    
    # Method 1: Try different preprocessing
    preprocessing_results = try_different_preprocessing(image)
    
    if preprocessing_results:
        print(f"✅ Found {len(preprocessing_results)} potential corner sets from preprocessing")
        corners = preprocessing_results[0][1]  # Use first result
        method = f"auto_{preprocessing_results[0][0]}"
    else:
        # Method 2: Estimate from image bounds
        print("🔄 Estimating corners from image bounds...")
        corners = estimate_corners_from_image_bounds(image, margin_ratio=0.02)
        method = "auto_bounds"
    
    # Show estimated corners and ask for confirmation
    vis_image = image.copy()
    labels = ["TL", "TR", "BR", "BL"]
    
    for i, (corner, label) in enumerate(zip(corners, labels)):
        x, y = int(corner[0]), int(corner[1])
        cv2.circle(vis_image, (x, y), 15, (0, 255, 0), -1)
        cv2.putText(vis_image, f"{i+1}:{label}", (x + 20, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    # Save visualization
    cv2.imwrite(os.path.join(output_dir, "estimated_corners.jpg"), vis_image)
    
    print(f"\n📍 Estimated corners:")
    for i, corner in enumerate(corners):
        print(f"   {i+1}. {labels[i]}: ({corner[0]:.1f}, {corner[1]:.1f})")
    
    # Ask user if they want to use estimated corners or select manually
    print(f"\n❓ Use estimated corners? (y/n/m for manual selection): ", end="")
    choice = input().lower().strip()
    
    if choice == 'm':
        # Manual selection
        selector = ManualCornerSelector(image)
        manual_corners = selector.select_corners()
        
        if manual_corners is not None:
            corners = manual_corners
            method = "manual"
            print("✅ Manual corners selected")
        else:
            print("❌ Manual selection cancelled, using estimated corners")
    elif choice == 'n':
        print("❌ Processing cancelled")
        return None
    
    # Process with selected corners
    print(f"\n🔄 Processing with {method} corners...")
    
    try:
        # Create configuration
        config = Config()
        target_size = config.target_size
        
        # Step 1: Perspective correction
        warped, H = warp_perspective(image, corners, target_size)
        cv2.imwrite(os.path.join(output_dir, "01_warped.jpg"), warped)
        print("✅ Perspective correction applied")
        
        # Step 2: Deskewing
        deskewed, angle = deskew_hough(warped, config.hough)
        cv2.imwrite(os.path.join(output_dir, "02_deskewed.jpg"), deskewed)
        print(f"✅ Deskewing applied: {angle:.2f}°")
        
        # Step 3: Binarization
        binary, stats = binarize_document(deskewed, config)
        cv2.imwrite(os.path.join(output_dir, "03_binary.jpg"), binary)
        print("✅ Binarization completed")
        
        # Create result object
        result = Result(
            warped_path=os.path.join(output_dir, "02_deskewed.jpg"),
            binarized_path=os.path.join(output_dir, "03_binary.jpg"),
            angle_deg=float(angle),
            H_3x3=H,
            quality=QualityMetrics(
                marker_count=4,
                skew_residual_deg=0.0,  # Would need to calculate
                edge_strength=0.5       # Would need to calculate
            ),
            debug=DebugInfo(
                corners_src=[(float(c[0]), float(c[1])) for c in corners],
                corners_dst=[
                    (0.0, 0.0),
                    (float(target_size.width-1), 0.0),
                    (float(target_size.width-1), float(target_size.height-1)),
                    (0.0, float(target_size.height-1))
                ],
                detection_method=method
            ),
            success=True
        )
        
        print(f"\n✅ Processing completed successfully!")
        print(f"📁 Output files saved to: {output_dir}/")
        print(f"📊 Method used: {method}")
        print(f"📐 Skew correction: {angle:.2f}°")
        
        return result
        
    except Exception as e:
        print(f"❌ Processing failed: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """Main function."""
    image_path = r"C:\Users\<USER>\Downloads\ptntest.jpg"
    
    # Check if image exists
    if not os.path.exists(image_path):
        print(f"❌ Image not found: {image_path}")
        return
    
    # Process with manual corner detection
    result = process_with_manual_corners(image_path)
    
    if result and result.success:
        print(f"\n🎉 SUCCESS! Document processed successfully.")
        print(f"📁 Check 'manual_output' folder for results")
    else:
        print(f"\n❌ Processing failed or was cancelled")


if __name__ == "__main__":
    main()
