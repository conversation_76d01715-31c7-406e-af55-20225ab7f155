"""
Marker detection module for document rectification.

Handles detection of 4 corner markers or page contours for perspective correction.
Includes fallback mechanisms when primary detection fails.
"""

import cv2
import numpy as np
from typing import Optional, List, Tuple
import math

from .types import Config, MarkerConfig, NotEnoughCornersError


def order_corners(points: np.ndarray) -> np.ndarray:
    """
    Order 4 corner points in clockwise order: TL, TR, BR, BL.
    
    Args:
        points: Array of 4 points [(x, y), ...]
        
    Returns:
        Ordered points as np.float32 array
    """
    points = points.reshape(4, 2)
    
    # Calculate sums and differences
    sums = points.sum(axis=1)
    diffs = np.diff(points, axis=1).flatten()
    
    # Top-left: minimum sum
    # Bottom-right: maximum sum  
    # Top-right: minimum difference (x - y)
    # Bottom-left: maximum difference (x - y)
    
    tl = points[np.argmin(sums)]
    br = points[np.argmax(sums)]
    tr = points[np.argmin(diffs)]
    bl = points[np.argmax(diffs)]
    
    return np.float32([tl, tr, br, bl])


def calculate_contour_properties(contour: np.ndarray) -> Tuple[float, float, Tuple[float, float]]:
    """
    Calculate properties of a contour for marker validation.
    
    Args:
        contour: Input contour
        
    Returns:
        Tuple of (area, aspect_ratio, centroid)
    """
    area = cv2.contourArea(contour)
    
    # Get minimum area rectangle
    rect = cv2.minAreaRect(contour)
    (_, _), (w, h), _ = rect
    
    # Calculate aspect ratio (closer to 1.0 = more square)
    if max(w, h) > 0:
        aspect_ratio = min(w, h) / max(w, h)
    else:
        aspect_ratio = 0.0
    
    # Calculate centroid
    M = cv2.moments(contour)
    if M["m00"] != 0:
        cx = M["m10"] / M["m00"]
        cy = M["m01"] / M["m00"]
    else:
        cx, cy = 0, 0
    
    return area, aspect_ratio, (cx, cy)


def distance_from_center(point: Tuple[float, float], image_shape: Tuple[int, int]) -> float:
    """Calculate distance from point to image center."""
    h, w = image_shape
    center_x, center_y = w / 2, h / 2
    return math.sqrt((point[0] - center_x) ** 2 + (point[1] - center_y) ** 2)


def find_four_markers(gray: np.ndarray, config: MarkerConfig) -> Optional[np.ndarray]:
    """
    Find 4 corner markers in the image.
    
    Args:
        gray: Input grayscale image
        config: Marker detection configuration
        
    Returns:
        4 corner points in order [TL, TR, BR, BL] or None if not found
    """
    h, w = gray.shape
    min_area = config.min_area_ratio * w * h
    
    # Apply Otsu thresholding and invert
    _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    thresh_inv = 255 - thresh
    
    # Find contours
    contours, _ = cv2.findContours(thresh_inv, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    candidates = []
    
    for contour in contours:
        area, aspect_ratio, centroid = calculate_contour_properties(contour)
        
        # Filter by area
        if area < min_area:
            continue
            
        # Approximate contour to polygon
        epsilon = config.approx_epsilon * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        # Check if approximately 4 corners
        if len(approx) == 4:
            # Check if reasonably square
            if aspect_ratio >= config.square_ratio_min:
                # Calculate distance from image center
                dist_from_center = distance_from_center(centroid, (h, w))
                
                candidates.append({
                    'centroid': centroid,
                    'distance': dist_from_center,
                    'area': area,
                    'aspect_ratio': aspect_ratio,
                    'approx': approx
                })
    
    # Sort by distance from center (descending - corners should be far from center)
    candidates.sort(key=lambda x: x['distance'], reverse=True)
    
    # Take top 4 candidates
    if len(candidates) < 4:
        return None
    
    # Extract centroids of top 4 markers
    marker_points = np.array([c['centroid'] for c in candidates[:4]], dtype=np.float32)
    
    # Order the corners
    ordered_corners = order_corners(marker_points)
    
    return ordered_corners


def find_page_corners(gray: np.ndarray, config: MarkerConfig) -> Optional[np.ndarray]:
    """
    Find page corners using contour detection (fallback method).
    
    Args:
        gray: Input grayscale image
        config: Marker detection configuration
        
    Returns:
        4 corner points in order [TL, TR, BR, BL] or None if not found
    """
    h, w = gray.shape
    min_area = 0.2 * w * h  # Page should be at least 20% of image
    
    # Apply Otsu thresholding and invert
    _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    thresh_inv = 255 - thresh
    
    # Find contours
    contours, _ = cv2.findContours(thresh_inv, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Find largest contour that could be the page
    best_contour = None
    best_area = 0
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if area > min_area and area > best_area:
            best_contour = contour
            best_area = area
    
    if best_contour is None:
        return None
    
    # Approximate contour to get corners
    epsilon = 0.02 * cv2.arcLength(best_contour, True)
    approx = cv2.approxPolyDP(best_contour, epsilon, True)
    
    # If we don't get exactly 4 points, try increasing epsilon
    max_attempts = 5
    attempt = 0
    while len(approx) != 4 and attempt < max_attempts:
        epsilon *= 1.5
        approx = cv2.approxPolyDP(best_contour, epsilon, True)
        attempt += 1
    
    if len(approx) != 4:
        return None
    
    # Convert to the right format and order
    corners = approx.reshape(4, 2).astype(np.float32)
    ordered_corners = order_corners(corners)
    
    return ordered_corners


def detect_corners(gray: np.ndarray, config: Config) -> Tuple[Optional[np.ndarray], str]:
    """
    Detect 4 corners using markers or page contour detection.
    
    Args:
        gray: Input grayscale image
        config: Complete configuration
        
    Returns:
        Tuple of (corners, detection_method)
        - corners: 4 corner points [TL, TR, BR, BL] or None
        - detection_method: "markers", "page_contour", or "failed"
    """
    # Try marker detection first
    corners = find_four_markers(gray, config.marker)
    if corners is not None:
        return corners, "markers"
    
    # Fallback to page contour detection if enabled
    if config.fallback.page_contour:
        corners = find_page_corners(gray, config.marker)
        if corners is not None:
            return corners, "page_contour"
    
    return None, "failed"


def validate_corners(corners: np.ndarray, image_shape: Tuple[int, int]) -> bool:
    """
    Validate that detected corners form a reasonable quadrilateral.
    
    Args:
        corners: 4 corner points
        image_shape: (height, width) of image
        
    Returns:
        True if corners are valid
    """
    if corners is None or len(corners) != 4:
        return False
    
    h, w = image_shape
    
    # Check if all corners are within image bounds
    for corner in corners:
        x, y = corner
        if x < 0 or x >= w or y < 0 or y >= h:
            return False
    
    # Check if corners form a convex quadrilateral
    # Calculate area using shoelace formula
    area = 0.5 * abs(sum(corners[i][0] * corners[(i + 1) % 4][1] - 
                        corners[(i + 1) % 4][0] * corners[i][1] 
                        for i in range(4)))
    
    # Area should be reasonable (at least 10% of image)
    min_area = 0.1 * w * h
    if area < min_area:
        return False
    
    return True
