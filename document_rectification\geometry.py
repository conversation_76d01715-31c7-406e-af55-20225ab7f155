"""
Geometry transformation module for document rectification.

Handles perspective warping, deskewing using <PERSON><PERSON> transform,
and optional thin-plate spline correction for curved documents.
"""

import cv2
import numpy as np
from typing import Tuple, List, Optional
import math

from .types import Config, HoughConfig, TargetSize


def warp_perspective(image: np.ndarray, src_corners: np.ndarray, target_size: TargetSize) -> Tuple[np.ndarray, np.ndarray]:
    """
    Apply perspective transformation to rectify document.
    
    Args:
        image: Input image (color or grayscale)
        src_corners: 4 source corners [TL, TR, BR, BL]
        target_size: Target output dimensions
        
    Returns:
        Tuple of (warped_image, homography_matrix)
    """
    # Define destination corners
    w, h = target_size.width, target_size.height
    dst_corners = np.float32([
        [0, 0],           # Top-left
        [w - 1, 0],       # Top-right
        [w - 1, h - 1],   # Bottom-right
        [0, h - 1]        # Bottom-left
    ])
    
    # Calculate homography matrix
    H = cv2.getPerspectiveTransform(src_corners, dst_corners)
    
    # Apply perspective transformation
    warped = cv2.warpPerspective(image, H, (w, h))
    
    return warped, H


def detect_skew_hough(image: np.ndarray, config: HoughConfig) -> float:
    """
    Detect skew angle using Hough line detection.
    
    Args:
        image: Input grayscale image
        config: Hough detection configuration
        
    Returns:
        Skew angle in degrees (positive = clockwise rotation needed)
    """
    # Convert to grayscale if needed
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()
    
    # Apply Canny edge detection
    edges = cv2.Canny(gray, config.canny_low, config.canny_high)
    
    # Detect lines using Hough transform
    lines = cv2.HoughLines(edges, 1, np.pi / 180, config.thresh)
    
    if lines is None or len(lines) == 0:
        return 0.0
    
    # Collect angles from detected lines
    angles = []
    
    for line in lines[:config.max_lines]:
        rho, theta = line[0]
        
        # Convert theta to angle in degrees
        angle = math.degrees(theta) - 90
        
        # Keep angles in reasonable range for text lines
        if -45 <= angle <= 45:
            angles.append(angle)
    
    if not angles:
        return 0.0
    
    # Return median angle as the skew
    angles.sort()
    n = len(angles)
    if n % 2 == 0:
        skew = (angles[n // 2 - 1] + angles[n // 2]) / 2
    else:
        skew = angles[n // 2]
    
    return skew


def deskew_image(image: np.ndarray, angle_deg: float) -> np.ndarray:
    """
    Rotate image to correct skew.
    
    Args:
        image: Input image
        angle_deg: Rotation angle in degrees (positive = clockwise)
        
    Returns:
        Deskewed image
    """
    if abs(angle_deg) < 0.1:  # Skip rotation for very small angles
        return image.copy()
    
    h, w = image.shape[:2]
    center = (w // 2, h // 2)
    
    # Create rotation matrix
    M = cv2.getRotationMatrix2D(center, -angle_deg, 1.0)
    
    # Apply rotation with border replication
    rotated = cv2.warpAffine(
        image, M, (w, h), 
        flags=cv2.INTER_LINEAR,
        borderMode=cv2.BORDER_REPLICATE
    )
    
    return rotated


def deskew_hough(image: np.ndarray, config: HoughConfig) -> Tuple[np.ndarray, float]:
    """
    Complete deskewing pipeline using Hough line detection.
    
    Args:
        image: Input image
        config: Hough configuration
        
    Returns:
        Tuple of (deskewed_image, angle_applied)
    """
    # Detect skew angle
    skew_angle = detect_skew_hough(image, config)
    
    # Apply deskewing
    deskewed = deskew_image(image, skew_angle)
    
    return deskewed, skew_angle


def calculate_skew_residual(image: np.ndarray, config: HoughConfig) -> float:
    """
    Calculate remaining skew after correction as a quality metric.
    
    Args:
        image: Corrected image
        config: Hough configuration
        
    Returns:
        Residual skew angle in degrees
    """
    residual_skew = detect_skew_hough(image, config)
    return abs(residual_skew)


def create_tps_grid(image_shape: Tuple[int, int], grid_size: Tuple[int, int]) -> Tuple[np.ndarray, np.ndarray]:
    """
    Create a regular grid for thin-plate spline transformation.
    
    Args:
        image_shape: (height, width) of image
        grid_size: (rows, cols) of grid
        
    Returns:
        Tuple of (source_points, target_points) for TPS
    """
    h, w = image_shape
    rows, cols = grid_size
    
    # Create regular grid
    y_coords = np.linspace(0, h - 1, rows)
    x_coords = np.linspace(0, w - 1, cols)
    
    source_points = []
    target_points = []
    
    for y in y_coords:
        for x in x_coords:
            source_points.append([x, y])
            target_points.append([x, y])  # Initially identical
    
    return np.float32(source_points), np.float32(target_points)


def apply_tps_correction(image: np.ndarray, source_points: np.ndarray, target_points: np.ndarray) -> np.ndarray:
    """
    Apply thin-plate spline correction for curved documents.
    
    Args:
        image: Input image
        source_points: Source control points
        target_points: Target control points
        
    Returns:
        TPS-corrected image
    """
    h, w = image.shape[:2]
    
    # Create TPS transformer
    tps = cv2.createThinPlateSplineShapeTransformer()
    
    # Reshape points for OpenCV
    source_shape = source_points.reshape(1, -1, 2)
    target_shape = target_points.reshape(1, -1, 2)
    
    # Estimate transformation
    tps.estimateTransformation(target_shape, source_shape, [])
    
    # Apply transformation
    corrected = cv2.warpAffine(image, np.eye(2, 3), (w, h))  # Placeholder
    
    # Note: Full TPS implementation would require more complex warping
    # This is a simplified version - in practice, you might use
    # scipy.interpolate.Rbf or implement custom TPS warping
    
    return corrected


def validate_homography(H: np.ndarray, image_shape: Tuple[int, int], target_size: TargetSize) -> bool:
    """
    Validate that homography matrix produces reasonable transformation.
    
    Args:
        H: Homography matrix
        image_shape: Original image shape
        target_size: Target dimensions
        
    Returns:
        True if homography is valid
    """
    if H is None:
        return False
    
    # Check if matrix is invertible
    det = np.linalg.det(H)
    if abs(det) < 1e-6:
        return False
    
    h, w = image_shape
    
    # Test corner transformations
    corners = np.float32([[0, 0], [w, 0], [w, h], [0, h]]).reshape(-1, 1, 2)
    transformed = cv2.perspectiveTransform(corners, H)
    
    # Check if transformed corners are within reasonable bounds
    for point in transformed:
        x, y = point[0]
        if x < -target_size.width or x > 2 * target_size.width or \
           y < -target_size.height or y > 2 * target_size.height:
            return False
    
    return True


def estimate_document_bounds(corners: np.ndarray) -> Tuple[float, float]:
    """
    Estimate document aspect ratio from corner points.
    
    Args:
        corners: 4 corner points [TL, TR, BR, BL]
        
    Returns:
        Tuple of (width, height) in pixels
    """
    tl, tr, br, bl = corners
    
    # Calculate widths and heights
    top_width = np.linalg.norm(tr - tl)
    bottom_width = np.linalg.norm(br - bl)
    left_height = np.linalg.norm(bl - tl)
    right_height = np.linalg.norm(br - tr)
    
    # Use average dimensions
    width = (top_width + bottom_width) / 2
    height = (left_height + right_height) / 2
    
    return width, height
