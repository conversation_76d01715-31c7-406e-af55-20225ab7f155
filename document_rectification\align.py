"""
Fine alignment module for document rectification.

Provides block-based template matching and thin-plate spline correction
for documents that require additional alignment after basic rectification.
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional, Dict, Any
import math

from .types import Config, FallbackConfig


class BlockAlignment:
    """Handles block-based alignment using template matching."""
    
    def __init__(self, config: FallbackConfig):
        self.config = config
        
    def divide_into_blocks(self, image: np.ndarray, grid_size: Tuple[int, int]) -> List[Dict[str, Any]]:
        """
        Divide image into blocks for individual alignment.
        
        Args:
            image: Input image
            grid_size: (rows, cols) for block division
            
        Returns:
            List of block information dictionaries
        """
        h, w = image.shape[:2]
        rows, cols = grid_size
        
        block_h = h // rows
        block_w = w // cols
        
        blocks = []
        
        for r in range(rows):
            for c in range(cols):
                y1 = r * block_h
                y2 = min((r + 1) * block_h, h)
                x1 = c * block_w
                x2 = min((c + 1) * block_w, w)
                
                block_roi = image[y1:y2, x1:x2]
                
                blocks.append({
                    'row': r,
                    'col': c,
                    'roi': (x1, y1, x2, y2),
                    'image': block_roi,
                    'center': ((x1 + x2) // 2, (y1 + y2) // 2),
                    'offset': (0, 0)  # Will be calculated
                })
        
        return blocks
    
    def find_block_offset(self, block_image: np.ndarray, template: np.ndarray, max_offset: int = 10) -> Tuple[int, int]:
        """
        Find optimal offset for a block using template matching.
        
        Args:
            block_image: Current block image
            template: Template to match against
            max_offset: Maximum allowed offset in pixels
            
        Returns:
            Tuple of (dx, dy) offset
        """
        if block_image.shape != template.shape:
            return (0, 0)
        
        # Use normalized cross-correlation
        result = cv2.matchTemplate(block_image, template, cv2.TM_CCOEFF_NORMED)
        
        # Find best match location
        _, max_val, _, max_loc = cv2.minMaxLoc(result)
        
        # Calculate offset from center
        h, w = template.shape[:2]
        center_x, center_y = w // 2, h // 2
        
        dx = max_loc[0] - center_x
        dy = max_loc[1] - center_y
        
        # Limit offset to reasonable range
        dx = max(-max_offset, min(max_offset, dx))
        dy = max(-max_offset, min(max_offset, dy))
        
        # Only return offset if match quality is good
        if max_val > 0.7:  # Threshold for good match
            return (dx, dy)
        else:
            return (0, 0)
    
    def detect_curvature_pattern(self, blocks: List[Dict[str, Any]]) -> bool:
        """
        Detect if block offsets follow a curvature pattern.
        
        Args:
            blocks: List of blocks with calculated offsets
            
        Returns:
            True if curvature pattern is detected
        """
        if len(blocks) < 9:  # Need at least 3x3 grid
            return False
        
        # Extract offsets
        offsets_x = [block['offset'][0] for block in blocks]
        offsets_y = [block['offset'][1] for block in blocks]
        
        # Check for systematic variation (curvature)
        # Simple heuristic: if offsets vary smoothly across the image
        
        # Calculate variance of offsets
        var_x = np.var(offsets_x)
        var_y = np.var(offsets_y)
        
        # If variance is significant and follows a pattern, likely curvature
        threshold_variance = 4.0  # pixels^2
        
        return var_x > threshold_variance or var_y > threshold_variance


class TPSCorrection:
    """Handles thin-plate spline correction for curved documents."""
    
    def __init__(self, config: FallbackConfig):
        self.config = config
        
    def create_control_points(self, image_shape: Tuple[int, int], blocks: List[Dict[str, Any]]) -> Tuple[np.ndarray, np.ndarray]:
        """
        Create control points for TPS from block offsets.
        
        Args:
            image_shape: Shape of the image
            blocks: List of blocks with calculated offsets
            
        Returns:
            Tuple of (source_points, target_points)
        """
        source_points = []
        target_points = []
        
        for block in blocks:
            center_x, center_y = block['center']
            dx, dy = block['offset']
            
            # Source point (where the block currently is)
            source_points.append([center_x + dx, center_y + dy])
            
            # Target point (where it should be)
            target_points.append([center_x, center_y])
        
        return np.float32(source_points), np.float32(target_points)
    
    def apply_tps_transform(self, image: np.ndarray, source_points: np.ndarray, target_points: np.ndarray) -> np.ndarray:
        """
        Apply thin-plate spline transformation.
        
        Args:
            image: Input image
            source_points: Source control points
            target_points: Target control points
            
        Returns:
            TPS-corrected image
        """
        h, w = image.shape[:2]
        
        # For OpenCV TPS, we need to use a different approach
        # This is a simplified implementation
        # In practice, you might want to use scipy.interpolate.Rbf
        
        try:
            # Create coordinate grids
            y_coords, x_coords = np.mgrid[0:h, 0:w]
            coords = np.column_stack([x_coords.ravel(), y_coords.ravel()])
            
            # Apply simple interpolation (placeholder for full TPS)
            # This would need proper TPS implementation
            corrected = image.copy()
            
            return corrected
            
        except Exception:
            # Fallback: return original image
            return image.copy()


def fine_align_document(image: np.ndarray, template: Optional[np.ndarray], config: Config) -> Tuple[np.ndarray, Dict[str, Any]]:
    """
    Apply fine alignment to document using block-based matching.
    
    Args:
        image: Input image (warped and deskewed)
        template: Optional template image for alignment
        config: Complete configuration
        
    Returns:
        Tuple of (aligned_image, alignment_stats)
    """
    stats = {
        'alignment_applied': False,
        'tps_applied': False,
        'block_count': 0,
        'avg_offset': (0.0, 0.0),
        'max_offset': (0, 0),
        'curvature_detected': False
    }
    
    # If TPS is not enabled, return original
    if not config.fallback.tps_enable:
        return image.copy(), stats
    
    # Convert to grayscale for processing
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()
    
    # If no template provided, use the image itself as template
    if template is None:
        template = gray.copy()
    elif len(template.shape) == 3:
        template = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
    
    # Initialize alignment components
    block_aligner = BlockAlignment(config.fallback)
    tps_corrector = TPSCorrection(config.fallback)
    
    # Divide image into blocks
    blocks = block_aligner.divide_into_blocks(gray, config.fallback.tps_grid)
    template_blocks = block_aligner.divide_into_blocks(template, config.fallback.tps_grid)
    
    stats['block_count'] = len(blocks)
    
    # Calculate offsets for each block
    offsets_x = []
    offsets_y = []
    
    for i, (block, template_block) in enumerate(zip(blocks, template_blocks)):
        offset = block_aligner.find_block_offset(
            block['image'], 
            template_block['image']
        )
        
        blocks[i]['offset'] = offset
        offsets_x.append(offset[0])
        offsets_y.append(offset[1])
    
    # Calculate statistics
    if offsets_x:
        stats['avg_offset'] = (float(np.mean(offsets_x)), float(np.mean(offsets_y)))
        stats['max_offset'] = (int(np.max(np.abs(offsets_x))), int(np.max(np.abs(offsets_y))))
    
    # Check if significant alignment is needed
    max_offset_magnitude = max(abs(stats['max_offset'][0]), abs(stats['max_offset'][1]))
    
    if max_offset_magnitude < 3:  # Less than 3 pixels offset
        return image.copy(), stats
    
    # Detect curvature pattern
    curvature_detected = block_aligner.detect_curvature_pattern(blocks)
    stats['curvature_detected'] = curvature_detected
    
    # Apply correction if curvature is detected
    if curvature_detected:
        # Create control points for TPS
        source_points, target_points = tps_corrector.create_control_points(
            gray.shape, blocks
        )
        
        # Apply TPS correction
        corrected = tps_corrector.apply_tps_transform(
            image, source_points, target_points
        )
        
        stats['tps_applied'] = True
        stats['alignment_applied'] = True
        
        return corrected, stats
    
    else:
        # Apply simple translation correction for uniform offsets
        avg_dx, avg_dy = stats['avg_offset']
        
        if abs(avg_dx) > 1 or abs(avg_dy) > 1:
            # Apply translation
            M = np.float32([[1, 0, -avg_dx], [0, 1, -avg_dy]])
            h, w = image.shape[:2]
            corrected = cv2.warpAffine(image, M, (w, h), borderMode=cv2.BORDER_REPLICATE)
            
            stats['alignment_applied'] = True
            return corrected, stats
    
    return image.copy(), stats


def validate_alignment_quality(original: np.ndarray, aligned: np.ndarray) -> Dict[str, float]:
    """
    Validate quality of alignment correction.
    
    Args:
        original: Original image before alignment
        aligned: Image after alignment
        
    Returns:
        Dictionary of alignment quality metrics
    """
    metrics = {
        'improvement_score': 0.0,
        'edge_alignment': 0.0,
        'structural_similarity': 0.0
    }
    
    # Convert to grayscale if needed
    if len(original.shape) == 3:
        orig_gray = cv2.cvtColor(original, cv2.COLOR_BGR2GRAY)
    else:
        orig_gray = original.copy()
        
    if len(aligned.shape) == 3:
        aligned_gray = cv2.cvtColor(aligned, cv2.COLOR_BGR2GRAY)
    else:
        aligned_gray = aligned.copy()
    
    # Calculate edge alignment improvement
    orig_edges = cv2.Canny(orig_gray, 50, 150)
    aligned_edges = cv2.Canny(aligned_gray, 50, 150)
    
    # Measure edge straightness (simplified)
    orig_edge_strength = np.sum(orig_edges) / 255.0
    aligned_edge_strength = np.sum(aligned_edges) / 255.0
    
    if orig_edge_strength > 0:
        metrics['edge_alignment'] = float(aligned_edge_strength / orig_edge_strength)
    
    # Calculate structural similarity (simplified)
    # In practice, you might use SSIM from skimage
    correlation = cv2.matchTemplate(orig_gray, aligned_gray, cv2.TM_CCOEFF_NORMED)
    metrics['structural_similarity'] = float(np.max(correlation))
    
    # Overall improvement score
    metrics['improvement_score'] = (metrics['edge_alignment'] + metrics['structural_similarity']) / 2
    
    return metrics
