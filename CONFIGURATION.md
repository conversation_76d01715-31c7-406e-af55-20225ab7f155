# Configuration Guide

This document provides detailed information about configuring the Document Rectification System for different types of OMR papers and processing requirements.

## Configuration Structure

The system uses a hierarchical configuration structure with the following main sections:

```python
from document_rectification import Config

config = Config()
# config.target_size     # Output dimensions
# config.illumination    # Illumination normalization
# config.clahe          # Contrast enhancement
# config.denoise        # Noise reduction
# config.unsharp        # Sharpening
# config.marker         # Marker detection
# config.hough          # Line detection for deskewing
# config.adaptive       # Adaptive thresholding
# config.binarize       # Final binarization
# config.fallback       # Fallback options
```

## Target Size Configuration

Controls the output dimensions of the rectified document.

```python
from document_rectification.types import TargetSize

# A4 at 300 DPI (default)
config.target_size = TargetSize(width=2480, height=3508)

# A4 at 150 DPI (faster processing)
config.target_size = TargetSize(width=1240, height=1754)

# Letter size at 300 DPI
config.target_size = TargetSize(width=2550, height=3300)

# Custom size
config.target_size = TargetSize(width=2000, height=3000)
```

## Preprocessing Configuration

### Illumination Normalization

```python
# Default: moderate background removal
config.illumination.morph_k = 41

# Strong background removal (uneven lighting)
config.illumination.morph_k = 61

# Light background removal (good lighting)
config.illumination.morph_k = 25
```

### CLAHE (Contrast Enhancement)

```python
# Default: balanced contrast
config.clahe.clip = 2.2
config.clahe.tile = (8, 8)

# High contrast (low-quality scans)
config.clahe.clip = 3.5
config.clahe.tile = (16, 16)

# Conservative contrast (high-quality scans)
config.clahe.clip = 1.5
config.clahe.tile = (4, 4)
```

### Denoising

```python
# Bilateral filter (default, preserves edges)
config.denoise.method = "bilateral"
config.denoise.d = 7
config.denoise.sigmaC = 50.0
config.denoise.sigmaS = 50.0

# Gaussian filter (faster, less edge preservation)
config.denoise.method = "gaussian"

# Strong denoising (very noisy images)
config.denoise.d = 9
config.denoise.sigmaC = 75.0
config.denoise.sigmaS = 75.0
```

### Unsharp Masking

```python
# Default sharpening
config.unsharp.sigma = 1.0
config.unsharp.amount = 1.0

# Strong sharpening (blurry images)
config.unsharp.sigma = 1.5
config.unsharp.amount = 1.5

# Light sharpening (already sharp images)
config.unsharp.sigma = 0.5
config.unsharp.amount = 0.5
```

## Marker Detection Configuration

### Basic Settings

```python
# Default: standard marker size
config.marker.min_area_ratio = 0.0002

# Small markers (high-resolution scans)
config.marker.min_area_ratio = 0.0001

# Large markers (low-resolution scans)
config.marker.min_area_ratio = 0.0005

# Square tolerance
config.marker.square_ratio_min = 0.75  # Default
config.marker.square_ratio_min = 0.6   # More tolerant
config.marker.square_ratio_min = 0.85  # Stricter
```

### Contour Approximation

```python
# Default approximation
config.marker.approx_epsilon = 0.04

# More precise (complex markers)
config.marker.approx_epsilon = 0.02

# Less precise (simple markers)
config.marker.approx_epsilon = 0.06
```

## Deskewing Configuration

### Hough Line Detection

```python
# Default settings
config.hough.canny_low = 50
config.hough.canny_high = 150
config.hough.thresh = 250
config.hough.max_lines = 200

# Sensitive detection (faint lines)
config.hough.canny_low = 30
config.hough.canny_high = 100
config.hough.thresh = 150

# Conservative detection (strong lines only)
config.hough.canny_low = 100
config.hough.canny_high = 200
config.hough.thresh = 400
config.hough.max_lines = 50
```

## Binarization Configuration

### Adaptive Thresholding

```python
# Gaussian method (default, better for varied lighting)
config.adaptive.method = "gaussian"
config.adaptive.block = 35
config.adaptive.C = 10.0

# Mean method (faster, uniform lighting)
config.adaptive.method = "mean"
config.adaptive.block = 25
config.adaptive.C = 8.0

# Large blocks (coarse features)
config.adaptive.block = 51
config.adaptive.C = 15.0

# Small blocks (fine features)
config.adaptive.block = 15
config.adaptive.C = 5.0
```

### Noise Removal

```python
# Default noise removal
config.binarize.median_blur = 3

# Strong noise removal
config.binarize.median_blur = 5

# No noise removal (clean images)
config.binarize.median_blur = 1
```

## Fallback Configuration

### Page Contour Detection

```python
# Enable page contour fallback (recommended)
config.fallback.page_contour = True

# Disable if markers are always present
config.fallback.page_contour = False
```

### Thin-Plate Spline Correction

```python
# Disable TPS (default, faster)
config.fallback.tps_enable = False

# Enable TPS for curved documents
config.fallback.tps_enable = True
config.fallback.tps_grid = (20, 20)

# Fine grid for detailed correction
config.fallback.tps_grid = (30, 30)

# Coarse grid for basic correction
config.fallback.tps_grid = (10, 10)
```

## Preset Configurations

### High Quality (Slow)

```python
def create_high_quality_config():
    config = Config()
    
    # Enhanced preprocessing
    config.clahe.clip = 3.0
    config.clahe.tile = (16, 16)
    config.denoise.d = 9
    config.unsharp.amount = 1.5
    
    # Sensitive marker detection
    config.marker.min_area_ratio = 0.00005
    config.marker.square_ratio_min = 0.6
    
    # Detailed line detection
    config.hough.thresh = 150
    config.hough.max_lines = 500
    
    # Fine binarization
    config.adaptive.block = 15
    config.binarize.median_blur = 5
    
    # Enable all fallbacks
    config.fallback.page_contour = True
    config.fallback.tps_enable = True
    
    return config
```

### Fast Processing

```python
def create_fast_config():
    config = Config()
    
    # Minimal preprocessing
    config.clahe.clip = 1.8
    config.clahe.tile = (4, 4)
    config.denoise.method = "gaussian"
    
    # Less sensitive marker detection
    config.marker.min_area_ratio = 0.001
    
    # Quick line detection
    config.hough.thresh = 300
    config.hough.max_lines = 50
    
    # Coarse binarization
    config.adaptive.block = 51
    config.binarize.median_blur = 1
    
    # Minimal fallbacks
    config.fallback.page_contour = False
    config.fallback.tps_enable = False
    
    return config
```

### Low Quality Scans

```python
def create_low_quality_config():
    config = Config()
    
    # Aggressive preprocessing
    config.illumination.morph_k = 61
    config.clahe.clip = 4.0
    config.clahe.tile = (16, 16)
    config.denoise.d = 9
    config.denoise.sigmaC = 100.0
    config.unsharp.amount = 2.0
    
    # Tolerant marker detection
    config.marker.min_area_ratio = 0.0001
    config.marker.square_ratio_min = 0.5
    config.marker.approx_epsilon = 0.08
    
    # Sensitive line detection
    config.hough.canny_low = 30
    config.hough.thresh = 100
    
    # Adaptive binarization
    config.adaptive.block = 25
    config.adaptive.C = 15.0
    config.binarize.median_blur = 7
    
    # All fallbacks enabled
    config.fallback.page_contour = True
    config.fallback.tps_enable = True
    
    return config
```

## Configuration Validation

```python
def validate_config(config):
    """Validate configuration parameters."""
    
    # Check target size
    assert config.target_size.width > 0
    assert config.target_size.height > 0
    
    # Check CLAHE parameters
    assert 1.0 <= config.clahe.clip <= 5.0
    assert all(t > 0 for t in config.clahe.tile)
    
    # Check marker parameters
    assert 0.00001 <= config.marker.min_area_ratio <= 0.01
    assert 0.1 <= config.marker.square_ratio_min <= 1.0
    
    # Check adaptive threshold
    assert config.adaptive.block % 2 == 1  # Must be odd
    assert config.adaptive.block >= 3
    
    return True
```

## Dynamic Configuration

```python
def auto_configure_from_image(image_path):
    """Automatically configure based on image properties."""
    import cv2
    
    image = cv2.imread(image_path)
    h, w = image.shape[:2]
    
    config = Config()
    
    # Adjust based on resolution
    if w * h > 4000000:  # High resolution
        config.marker.min_area_ratio = 0.00005
        config.clahe.tile = (16, 16)
    elif w * h < 1000000:  # Low resolution
        config.marker.min_area_ratio = 0.001
        config.clahe.tile = (4, 4)
    
    # Adjust based on image quality
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    contrast = np.std(gray)
    
    if contrast < 30:  # Low contrast
        config.clahe.clip = 3.5
        config.unsharp.amount = 1.5
    elif contrast > 80:  # High contrast
        config.clahe.clip = 1.5
        config.unsharp.amount = 0.5
    
    return config
```
