#!/usr/bin/env python3
"""
Diagnostic script to analyze the ptntest.jpg image and understand
why corner detection is failing.
"""

import os
import sys
import cv2
import numpy as np

# Add the document_rectification module to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from document_rectification.preprocess import preprocess_image
from document_rectification.markers import find_four_markers, find_page_corners
from document_rectification import Config


def analyze_image(image_path):
    """Comprehensive analysis of the input image."""
    
    print("=" * 60)
    print("IMAGE ANALYSIS AND DIAGNOSTICS")
    print("=" * 60)
    
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        print(f"❌ Could not load image: {image_path}")
        return
    
    h, w, c = image.shape
    print(f"📊 Image dimensions: {w}x{h} pixels")
    print(f"📊 Channels: {c}")
    print(f"📊 Total pixels: {w*h:,}")
    
    # Convert to grayscale for analysis
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Basic statistics
    print(f"\n📈 Pixel statistics:")
    print(f"   • Mean brightness: {np.mean(gray):.1f}")
    print(f"   • Std deviation: {np.std(gray):.1f}")
    print(f"   • Min value: {np.min(gray)}")
    print(f"   • Max value: {np.max(gray)}")
    print(f"   • Dynamic range: {np.max(gray) - np.min(gray)}")
    
    # Create output directory
    output_dir = "diagnostic_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # Save original grayscale
    cv2.imwrite(os.path.join(output_dir, "00_original_gray.jpg"), gray)
    
    # Test preprocessing
    print(f"\n🔄 Testing preprocessing...")
    config = Config()
    enhanced_gray, original_color, stats = preprocess_image(image, config)
    
    print(f"   • Original mean: {stats['original_mean']:.1f}")
    print(f"   • Final mean: {stats['final_mean']:.1f}")
    print(f"   • Final std: {stats['final_std']:.1f}")
    
    # Save preprocessed image
    cv2.imwrite(os.path.join(output_dir, "01_preprocessed.jpg"), enhanced_gray)
    
    # Test thresholding
    print(f"\n🔄 Testing thresholding methods...")
    
    # Otsu thresholding
    _, otsu_thresh = cv2.threshold(enhanced_gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    otsu_inv = 255 - otsu_thresh
    cv2.imwrite(os.path.join(output_dir, "02_otsu_threshold.jpg"), otsu_thresh)
    cv2.imwrite(os.path.join(output_dir, "02_otsu_inverted.jpg"), otsu_inv)
    
    # Adaptive thresholding
    adaptive_thresh = cv2.adaptiveThreshold(enhanced_gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 35, 10)
    cv2.imwrite(os.path.join(output_dir, "03_adaptive_threshold.jpg"), adaptive_thresh)
    
    # Find contours on different thresholded images
    print(f"\n🔍 Analyzing contours...")
    
    test_images = [
        ("Original Gray", gray),
        ("Enhanced", enhanced_gray),
        ("Otsu Inverted", otsu_inv),
        ("Adaptive", 255 - adaptive_thresh)
    ]
    
    for name, test_img in test_images:
        contours, _ = cv2.findContours(test_img, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        print(f"   • {name}: {len(contours)} contours found")
        
        # Analyze contour sizes
        if contours:
            areas = [cv2.contourArea(c) for c in contours]
            areas.sort(reverse=True)
            print(f"     - Largest areas: {areas[:5]}")
            print(f"     - Area ratios: {[a/(w*h) for a in areas[:5]]}")
    
    # Test marker detection with different parameters
    print(f"\n🔍 Testing marker detection with various parameters...")
    
    test_configs = [
        ("Default", 0.0002, 0.75),
        ("Very Sensitive", 0.00001, 0.5),
        ("Large Markers", 0.001, 0.8),
        ("Tiny Markers", 0.000001, 0.3)
    ]
    
    for name, min_area, square_ratio in test_configs:
        config.marker.min_area_ratio = min_area
        config.marker.square_ratio_min = square_ratio
        
        corners = find_four_markers(enhanced_gray, config.marker)
        print(f"   • {name} (area≥{min_area}, square≥{square_ratio}): {'✅' if corners is not None else '❌'}")
        
        if corners is not None:
            print(f"     - Found corners at: {corners}")
    
    # Test page contour detection
    print(f"\n🔍 Testing page contour detection...")
    page_corners = find_page_corners(enhanced_gray, config.marker)
    print(f"   • Page contour detection: {'✅' if page_corners is not None else '❌'}")
    
    if page_corners is not None:
        print(f"     - Found corners at: {page_corners}")
    
    # Create visualization of largest contours
    print(f"\n🎨 Creating contour visualizations...")
    
    # Find and draw largest contours
    contours, _ = cv2.findContours(otsu_inv, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Sort by area
    contours = sorted(contours, key=cv2.contourArea, reverse=True)
    
    # Draw top 20 contours
    vis_image = cv2.cvtColor(enhanced_gray, cv2.COLOR_GRAY2BGR)
    
    for i, contour in enumerate(contours[:20]):
        area = cv2.contourArea(contour)
        color = (0, 255 - i*10, i*10)  # Different colors for each contour
        cv2.drawContours(vis_image, [contour], -1, color, 2)
        
        # Draw contour number
        M = cv2.moments(contour)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            cv2.putText(vis_image, str(i), (cx, cy), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
    
    cv2.imwrite(os.path.join(output_dir, "04_top_contours.jpg"), vis_image)
    
    # Test different approximation epsilons
    print(f"\n🔍 Testing contour approximation...")
    
    if contours:
        largest_contour = contours[0]
        perimeter = cv2.arcLength(largest_contour, True)
        
        epsilons = [0.01, 0.02, 0.04, 0.06, 0.08, 0.1]
        
        for eps in epsilons:
            approx = cv2.approxPolyDP(largest_contour, eps * perimeter, True)
            print(f"   • Epsilon {eps}: {len(approx)} points")
            
            if len(approx) == 4:
                print(f"     ✅ Found 4-point approximation with epsilon {eps}")
                print(f"     - Points: {approx.reshape(4, 2)}")
    
    # Edge detection analysis
    print(f"\n🔍 Edge detection analysis...")
    
    edges = cv2.Canny(enhanced_gray, 50, 150)
    cv2.imwrite(os.path.join(output_dir, "05_edges.jpg"), edges)
    
    edge_pixels = np.sum(edges > 0)
    edge_ratio = edge_pixels / (w * h)
    print(f"   • Edge pixels: {edge_pixels:,}")
    print(f"   • Edge ratio: {edge_ratio:.4f}")
    
    # Hough line detection
    lines = cv2.HoughLines(edges, 1, np.pi/180, 100)
    if lines is not None:
        print(f"   • Hough lines detected: {len(lines)}")
        
        # Draw lines
        line_image = cv2.cvtColor(enhanced_gray, cv2.COLOR_GRAY2BGR)
        for line in lines[:20]:  # Draw first 20 lines
            rho, theta = line[0]
            a = np.cos(theta)
            b = np.sin(theta)
            x0 = a * rho
            y0 = b * rho
            x1 = int(x0 + 1000 * (-b))
            y1 = int(y0 + 1000 * (a))
            x2 = int(x0 - 1000 * (-b))
            y2 = int(y0 - 1000 * (a))
            cv2.line(line_image, (x1, y1), (x2, y2), (0, 0, 255), 1)
        
        cv2.imwrite(os.path.join(output_dir, "06_hough_lines.jpg"), line_image)
    else:
        print(f"   • No Hough lines detected")
    
    print(f"\n📁 Diagnostic images saved to: {output_dir}/")
    print(f"📄 Check the images to understand the detection issues")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    
    if np.std(gray) < 30:
        print("   • Low contrast detected - increase CLAHE clip limit")
    
    if edge_ratio < 0.01:
        print("   • Few edges detected - check image quality")
    
    if len(contours) < 10:
        print("   • Few contours found - adjust thresholding parameters")
    
    print("   • Check diagnostic images in 'diagnostic_output' folder")
    print("   • Look for 4 distinct corner markers or clear page boundary")
    print("   • If markers are too small, decrease min_area_ratio")
    print("   • If markers are not square, decrease square_ratio_min")


def main():
    """Main function."""
    image_path = r"C:\Users\<USER>\Downloads\ptntest.jpg"
    analyze_image(image_path)


if __name__ == "__main__":
    main()
